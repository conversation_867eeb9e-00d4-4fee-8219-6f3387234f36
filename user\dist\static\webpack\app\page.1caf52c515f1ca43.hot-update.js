"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/statusStyles.ts":
/*!***********************************!*\
  !*** ./src/utils/statusStyles.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_STYLES: () => (/* binding */ STATUS_STYLES),\n/* harmony export */   getStatusFromScore: () => (/* binding */ getStatusFromScore),\n/* harmony export */   getStatusStyles: () => (/* binding */ getStatusStyles),\n/* harmony export */   statusFromScoreWithStyles: () => (/* binding */ statusFromScoreWithStyles)\n/* harmony export */ });\n// Status styles helper\n// Provides three status variants and helpers to select styles.\n/**\r\n * Tailwind class based style map for three status variants.\r\n * Use these values directly as `className` fragments in components.\r\n */ const STATUS_STYLES = {\n    good: {\n        statusColour: \"bg-green-600\",\n        statusBorderColour: \"border-green-600\",\n        cardBG: \"bg-green-50\",\n        cardBorderColour: \"border-green-100\"\n    },\n    warning: {\n        statusColour: \"bg-yellow-600\",\n        statusBorderColour: \"border-yellow-600\",\n        cardBG: \"bg-yellow-50\",\n        cardBorderColour: \"border-yellow-100\"\n    },\n    danger: {\n        statusColour: \"bg-red-600\",\n        statusBorderColour: \"border-[]\",\n        cardBG: \"bg-red-50\",\n        cardBorderColour: \"border-red-100\"\n    }\n};\n/**\r\n * Return the style object for a given variant. Defaults to `warning` if unknown.\r\n */ function getStatusStyles(variant) {\n    if (!variant || !(variant in STATUS_STYLES)) return STATUS_STYLES.warning;\n    return STATUS_STYLES[variant];\n}\n/**\r\n * Simple heuristic to map a numeric score (0-100) to a status variant.\r\n * - >= 80 => good\r\n * - >= 50 => warning\r\n * - < 50 => danger\r\n * Useful when backend returns a score/health metric and you want a visual status.\r\n */ function getStatusFromScore(score) {\n    if (typeof score !== \"number\" || Number.isNaN(score)) return \"warning\";\n    if (score >= 80) return \"good\";\n    if (score >= 50) return \"warning\";\n    return \"danger\";\n}\n/**\r\n * Convenience: return both variant and styles from a score\r\n */ function statusFromScoreWithStyles(score) {\n    const variant = getStatusFromScore(score !== null && score !== void 0 ? score : null);\n    return {\n        variant,\n        styles: getStatusStyles(variant)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusStyles.ts\n"));

/***/ })

});