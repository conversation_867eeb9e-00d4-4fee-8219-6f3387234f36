"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/FirstLandingDash */ \"(app-pages-browser)/./src/components/dashboard/FirstLandingDash.tsx\");\n/* harmony import */ var _components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/TopTrendsTable */ \"(app-pages-browser)/./src/components/dashboard/TopTrendsTable.tsx\");\n/* harmony import */ var _components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/NewListedCoins */ \"(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/MainDash */ \"(app-pages-browser)/./src/components/dashboard/MainDash.tsx\");\n/* harmony import */ var _components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/AccountTabContent */ \"(app-pages-browser)/./src/components/dashboard/AccountTabContent.tsx\");\n/* harmony import */ var _components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransactionsTabContent */ \"(app-pages-browser)/./src/components/dashboard/TransactionsTabContent.tsx\");\n/* harmony import */ var _lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/dashboard-data */ \"(app-pages-browser)/./src/lib/dashboard-data.ts\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardHomePage() {\n    _s();\n    const { isMobile, isTablet, isDesktop } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMainDash, setShowMainDash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceToggle, setForceToggle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch dashboard data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardHomePage.useEffect\": ()=>{\n            const loadDashboardData = {\n                \"DashboardHomePage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__.fetchDashboardData)();\n                        setDashboardData(data);\n                        setShowMainDash(data.hasUserData);\n                    } catch (error) {\n                        console.error('Failed to load dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardHomePage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardHomePage.useEffect\"], []);\n    // Handle toggle between dashboards\n    const handleToggleDashboard = ()=>{\n        setForceToggle(!forceToggle);\n        setShowMainDash(!showMainDash);\n    };\n    var _dashboardData_hasUserData;\n    // Determine which dashboard to show\n    const shouldShowMainDash = forceToggle ? showMainDash : (_dashboardData_hasUserData = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.hasUserData) !== null && _dashboardData_hasUserData !== void 0 ? _dashboardData_hasUserData : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm sm:text-base\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (!dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm sm:text-base\",\n                    children: \"Failed to load dashboard data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // Mobile and Tablet Layout (< 1200px)\n    if (isMobile || isTablet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex flex-col gap-2 sm:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 p-2 sm:p-3 bg-white rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"dashboard-toggle\",\n                                    className: \"text-xs sm:text-sm font-medium\",\n                                    children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    id: \"dashboard-toggle\",\n                                    checked: shouldShowMainDash,\n                                    onCheckedChange: handleToggleDashboard\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                            loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ltvChartData: dashboardData.ltvChartData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full p-2 sm:p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"updates\",\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 mb-2 sm:mb-4 bg-gray-100 p-1 rounded-full h-10 sm:h-12 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"updates\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"account\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"transactions\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-0 overflow-y-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"updates\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 sm:space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            trends: dashboardData.topTrendsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            coins: dashboardData.newCoinsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"account\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"transactions\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full grid grid-cols-1 gap-4 lg:grid-cols-[6fr_4fr] 2xl:grid-cols-[7fr_3fr]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"dashboard-toggle-desktop\",\n                                        className: \"text-sm font-medium\",\n                                        children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                        id: \"dashboard-toggle-desktop\",\n                                        checked: shouldShowMainDash,\n                                        onCheckedChange: handleToggleDashboard\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0\",\n                            children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                                loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ltvChartData: dashboardData.ltvChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"h-full w-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            defaultValue: \"updates\",\n                            className: \"h-full flex flex-col bg-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid w-full grid-cols-3 mb-4 p-1 bg-transparent rounded-full h-12 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"updates\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"account\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"transactions\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-h-full min-w-full overflow-y-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"updates\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        trends: dashboardData.topTrendsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        coins: dashboardData.newCoinsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"account\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"transactions\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardHomePage, \"AjWREQYOBB9rwT0eFkHbJXzz0/o=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType\n    ];\n});\n_c = DashboardHomePage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});