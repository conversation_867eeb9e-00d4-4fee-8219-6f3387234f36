"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Bitcoin, FileTerminal, FileType } from "lucide-react"
import { CryptoCurrency, LoanHealth } from "@/types/enums"
import { getStatusStyles, getStatusFromLoanHealth } from "@/utils/statusStyles"

interface LoanCardProps {
  id: string;
  tokenTicker: CryptoCurrency;
  loanAmount?: number;
  loanValueUSDT?: number;
  status: LoanHealth;
  marginCallAmount?: number;
}

const getCryptoIcon = (crypto: CryptoCurrency) => {
  switch (crypto) {
    case CryptoCurrency.BTC:
      return Bitcoin;
    case CryptoCurrency.ETH:
      return FileTerminal;
    case CryptoCurrency.USDT:
      return FileType;
    default:
      return Bitcoin;
  }
};

const getCryptoIconColor = (crypto: CryptoCurrency) => {
  switch (crypto) {
    case CryptoCurrency.BTC:
      return "#F7931A"; // Bitcoin orange
    case CryptoCurrency.ETH:
      return "#627EEA"; // Ethereum blue
    case CryptoCurrency.USDT:
      return "#26A17B"; // USDT green
    default:
      return "#F7931A";
  }
};

export default function LoanCard({ 
  id, 
  tokenTicker, 
  loanAmount = 0, 
  loanValueUSDT = 0, 
  status, 
  marginCallAmount = 0 
}: LoanCardProps) {
  const statusVariant = getStatusFromLoanHealth(status);
  const statusStyles = getStatusStyles(statusVariant);
  const IconComponent = getCryptoIcon(tokenTicker);
  const iconColor = getCryptoIconColor(tokenTicker);

  // Add safety checks for numeric values
  const safeLoanAmount = typeof loanAmount === 'number' && !isNaN(loanAmount) ? loanAmount : 0;
  const safeLoanValueUSDT = typeof loanValueUSDT === 'number' && !isNaN(loanValueUSDT) ? loanValueUSDT : 0;
  const safeMarginCallAmount = typeof marginCallAmount === 'number' && !isNaN(marginCallAmount) ? marginCallAmount : 0;

  return (
    <Card
      className="relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0 p-3"
      style={{
        backgroundColor: statusStyles.cardBG,
        borderColor: statusStyles.cardBorderColour,
      }}
    >
      <CardContent className="h-full flex flex-col justify-between">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2 md:gap-3">
            <div 
              className="w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center"
              style={{ backgroundColor: iconColor }}
            >
              <IconComponent 
                size={20} 
                color="white" 
                className="md:w-6 md:h-6"
              />
            </div>
            <div className="flex flex-col">
              <p className="text-sm md:text-base font-semibold">
                {safeLoanAmount} {tokenTicker}
              </p>
              <p className="text-xs text-muted-foreground">
                ${safeLoanValueUSDT.toFixed(2)} USDT
              </p>
            </div>
          </div>
          <div 
            className="flex items-center justify-center rounded-full p-1 border"
            style={{ borderColor: statusStyles.statusBorderColour }}
          >
            <div 
              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
              style={{ backgroundColor: statusStyles.statusColour }}
            />
          </div>
        </div>

        <div className="mt-3 md:mt-4 space-y-1">
          <p className="text-xs text-muted-foreground capitalize">Margin call</p>
          <p className="text-xs md:text-sm font-semibold tracking-tight">
            ${safeMarginCallAmount.toLocaleString()}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}