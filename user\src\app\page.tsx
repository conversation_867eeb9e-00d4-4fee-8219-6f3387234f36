"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import FirstLandingDash from "@/components/dashboard/FirstLandingDash";
import TopTrendsTable from "@/components/dashboard/TopTrendsTable";
import NewListedCoins from "@/components/dashboard/NewListedCoins";
import { useViewportType } from "@/hooks/use-viewport";
import MainDash from "@/components/dashboard/MainDash";
import AccountTabContent from "@/components/dashboard/AccountTabContent";
import TransactionsTabContent from "@/components/dashboard/TransactionsTabContent";
import { fetchDashboardData, DashboardData } from "@/lib/dashboard-data";
import { mockInterestData, mockLoanData } from "@/data/loanDashboardMockData"

export default function DashboardHomePage() {
  const { isMobile } = useViewportType();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showMainDash, setShowMainDash] = useState(false);
  const [forceToggle, setForceToggle] = useState(false);

  // Fetch dashboard data on component mount
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const data = await fetchDashboardData();
        setDashboardData(data);
        setShowMainDash(data.hasUserData);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Handle toggle between dashboards
  const handleToggleDashboard = () => {
    setForceToggle(!forceToggle);
    setShowMainDash(!showMainDash);
  };

  // Determine which dashboard to show
  const shouldShowMainDash = forceToggle ? showMainDash : (dashboardData?.hasUserData ?? false);

  if (loading) {
    return (
      <div className="w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Failed to load dashboard data</p>
        </div>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="w-full h-screen p-4 bg-gray-300/50 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Toggle Control */}
          <div className="flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <Label htmlFor="dashboard-toggle" className="text-sm font-medium">
                {shouldShowMainDash ? "Main Dashboard" : "First Landing"}
              </Label>
              <Switch
                id="dashboard-toggle"
                checked={shouldShowMainDash}
                onCheckedChange={handleToggleDashboard}
              />
            </div>
          </div>

          <div className="flex-1 min-h-0">
            {shouldShowMainDash ? (
              <MainDash
                interestData={mockInterestData}
                loanData={mockLoanData}
              />
            ) : (
              <FirstLandingDash
                ltvChartData={dashboardData.ltvChartData}
              />
            )}
          </div>

          <div className="flex-1 mt-4 min-h-0">
            <Card className="h-full p-4">
              <Tabs defaultValue="updates" className="h-full flex flex-col">
                <TabsList className="grid w-full grid-cols-3 mb-4 bg-gray-100 p-1 rounded-full h-12 flex-shrink-0">
                  <TabsTrigger
                    value="updates"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                  >
                    Updates
                  </TabsTrigger>
                  <TabsTrigger
                    value="account"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                  >
                    Account
                  </TabsTrigger>
                  <TabsTrigger
                    value="transactions"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                  >
                    Transactions
                  </TabsTrigger>
                </TabsList>

                <div className="flex-1 min-h-0 overflow-y-auto">
                  <TabsContent value="updates" className="space-y-4 px-3">
                    <div className="space-y-4">
                      <TopTrendsTable trends={dashboardData.topTrendsData} />
                      <NewListedCoins coins={dashboardData.newCoinsData} />
                    </div>
                  </TabsContent>

                  <TabsContent value="account" className="space-y-4 px-3">
                    <AccountTabContent />
                  </TabsContent>

                  <TabsContent value="transactions" className="space-y-4 px-3">
                    <TransactionsTabContent />
                  </TabsContent>
                </div>
              </Tabs>
            </Card>
          </div>
        </div>
      </div>
    )
  };

  return (
    <div className="w-full h-screen p-4 bg-gray-300/50 overflow-hidden">
      <div className="h-full grid grid-cols-1 gap-4 lg:grid-cols-[6fr_4fr] 2xl:grid-cols-[7fr_3fr]">
        <div className="min-h-0 flex flex-col">
          {/* Toggle Control */}
          <div className="flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <Label htmlFor="dashboard-toggle-desktop" className="text-sm font-medium">
                {shouldShowMainDash ? "Main Dashboard" : "First Landing"}
              </Label>
              <Switch
                id="dashboard-toggle-desktop"
                checked={shouldShowMainDash}
                onCheckedChange={handleToggleDashboard}
              />
            </div>
          </div>

          <div className="flex-1 min-h-0">
            {shouldShowMainDash ? (
              <MainDash
                interestData={mockInterestData}
                loanData={mockLoanData}
              />
            ) : (
              <FirstLandingDash
                ltvChartData={dashboardData.ltvChartData}
              />
            )}
          </div>
        </div>

        <div className="min-h-0 w-full">
          <Card className="h-full w-full p-4">
            <Tabs defaultValue="updates" className="h-full flex flex-col bg-transparent">
              <TabsList className="grid w-full grid-cols-3 mb-4 p-1 bg-transparent rounded-full h-12 flex-shrink-0">
                <TabsTrigger
                  value="updates"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                >
                  Updates
                </TabsTrigger>
                <TabsTrigger
                  value="account"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                >
                  Account
                </TabsTrigger>
                <TabsTrigger
                  value="transactions"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full"
                >
                  Transactions
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 min-h-full min-w-full overflow-y-auto">
                <TabsContent value="updates" className="space-y-4 px-3">
                  <div className="space-y-4">
                    <TopTrendsTable trends={dashboardData.topTrendsData} />
                    <NewListedCoins coins={dashboardData.newCoinsData} />
                  </div>
                </TabsContent>

                <TabsContent value="account" className="space-y-4 px-3">
                  <AccountTabContent />
                </TabsContent>

                <TabsContent value="transactions" className="space-y-4 px-3">
                  <TransactionsTabContent />
                </TabsContent>
              </div>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  );
}