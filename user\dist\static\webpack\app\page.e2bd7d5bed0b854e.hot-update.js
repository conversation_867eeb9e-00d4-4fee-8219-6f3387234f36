"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/FirstLandingDash */ \"(app-pages-browser)/./src/components/dashboard/FirstLandingDash.tsx\");\n/* harmony import */ var _components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/TopTrendsTable */ \"(app-pages-browser)/./src/components/dashboard/TopTrendsTable.tsx\");\n/* harmony import */ var _components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/NewListedCoins */ \"(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/MainDash */ \"(app-pages-browser)/./src/components/dashboard/MainDash.tsx\");\n/* harmony import */ var _components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/AccountTabContent */ \"(app-pages-browser)/./src/components/dashboard/AccountTabContent.tsx\");\n/* harmony import */ var _components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransactionsTabContent */ \"(app-pages-browser)/./src/components/dashboard/TransactionsTabContent.tsx\");\n/* harmony import */ var _lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/dashboard-data */ \"(app-pages-browser)/./src/lib/dashboard-data.ts\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardHomePage() {\n    _s();\n    const { isMobile, isTablet, isDesktop } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMainDash, setShowMainDash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceToggle, setForceToggle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch dashboard data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardHomePage.useEffect\": ()=>{\n            const loadDashboardData = {\n                \"DashboardHomePage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__.fetchDashboardData)();\n                        setDashboardData(data);\n                        setShowMainDash(data.hasUserData);\n                    } catch (error) {\n                        console.error('Failed to load dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardHomePage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardHomePage.useEffect\"], []);\n    // Handle toggle between dashboards\n    const handleToggleDashboard = ()=>{\n        setForceToggle(!forceToggle);\n        setShowMainDash(!showMainDash);\n    };\n    var _dashboardData_hasUserData;\n    // Determine which dashboard to show\n    const shouldShowMainDash = forceToggle ? showMainDash : (_dashboardData_hasUserData = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.hasUserData) !== null && _dashboardData_hasUserData !== void 0 ? _dashboardData_hasUserData : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm sm:text-base\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (!dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-sm sm:text-base\",\n                    children: \"Failed to load dashboard data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    // Mobile and Tablet Layout (< 1200px)\n    if (isMobile || isTablet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex flex-col gap-2 sm:gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 p-2 sm:p-3 bg-white rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"dashboard-toggle\",\n                                    className: \"text-xs sm:text-sm font-medium\",\n                                    children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    id: \"dashboard-toggle\",\n                                    checked: shouldShowMainDash,\n                                    onCheckedChange: handleToggleDashboard\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                            loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ltvChartData: dashboardData.ltvChartData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full p-2 sm:p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"updates\",\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 mb-2 sm:mb-4 bg-gray-100 p-1 rounded-full h-10 sm:h-12 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"updates\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"account\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"transactions\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-0 overflow-y-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"updates\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2 sm:space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            trends: dashboardData.topTrendsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            coins: dashboardData.newCoinsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"account\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"transactions\",\n                                                className: \"space-y-2 sm:space-y-4 px-1 sm:px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop Layout (≥ 1200px)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 lg:p-6 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[2fr_1fr] 2xl:grid-cols-[7fr_3fr] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mb-4 lg:mb-6 p-3 lg:p-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"dashboard-toggle-desktop\",\n                                        className: \"text-sm lg:text-base font-medium\",\n                                        children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                        id: \"dashboard-toggle-desktop\",\n                                        checked: shouldShowMainDash,\n                                        onCheckedChange: handleToggleDashboard\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                            children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                                loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ltvChartData: dashboardData.ltvChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full w-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"h-full w-full p-3 lg:p-4 xl:p-6 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            defaultValue: \"updates\",\n                            className: \"h-full flex flex-col bg-transparent overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid w-full grid-cols-3 mb-4 lg:mb-6 p-1 bg-gray-100 rounded-full h-10 lg:h-12 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"updates\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm\",\n                                            children: \"Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"account\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm\",\n                                            children: \"Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"transactions\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm\",\n                                            children: \"Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-h-0 max-h-full overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"updates\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 lg:space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        trends: dashboardData.topTrendsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        coins: dashboardData.newCoinsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"account\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"transactions\",\n                                            className: \"h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardHomePage, \"AjWREQYOBB9rwT0eFkHbJXzz0/o=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType\n    ];\n});\n_c = DashboardHomePage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});