"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/bitcoin.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Bitcoin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727\",\n            key: \"yr8idg\"\n        }\n    ]\n];\nconst Bitcoin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"bitcoin\", __iconNode);\n //# sourceMappingURL=bitcoin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-terminal.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-terminal.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileTerminal)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m8 16 2-2-2-2\",\n            key: \"10vzyd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 18h4\",\n            key: \"1wd2n7\"\n        }\n    ]\n];\nconst FileTerminal = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-terminal\", __iconNode);\n //# sourceMappingURL=file-terminal.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-terminal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-type.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-type.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileType)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.536.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 13v-1h6v1\",\n            key: \"1bb014\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 12v6\",\n            key: \"3ahymv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M11 18h2\",\n            key: \"12mj7e\"\n        }\n    ]\n];\nconst FileType = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-type\", __iconNode);\n //# sourceMappingURL=file-type.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-type.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/LoanCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/LoanCard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoanCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-type.js\");\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n/* harmony import */ var _utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/statusStyles */ \"(app-pages-browser)/./src/utils/statusStyles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst getCryptoIcon = (crypto)=>{\n    switch(crypto){\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.BTC:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.ETH:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.USDT:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        default:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    }\n};\nconst getCryptoIconColor = (crypto)=>{\n    switch(crypto){\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.BTC:\n            return \"#F7931A\"; // Bitcoin orange\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.ETH:\n            return \"#627EEA\"; // Ethereum blue\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.USDT:\n            return \"#26A17B\"; // USDT green\n        default:\n            return \"#F7931A\";\n    }\n};\nfunction LoanCard(param) {\n    let { id, tokenTicker, loanAmount, loanValueUSDT, status, marginCallAmount } = param;\n    const statusVariant = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__.getStatusFromLoanHealth)(status);\n    const statusStyles = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__.getStatusStyles)(statusVariant);\n    const IconComponent = getCryptoIcon(tokenTicker);\n    const iconColor = getCryptoIconColor(tokenTicker);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"relative overflow-hidden rounded-2xl border-2 shadow-sm h-full min-h-0\",\n        style: {\n            backgroundColor: statusStyles.cardBG,\n            borderColor: statusStyles.cardBorderColour\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"p-3 md:p-4 h-full flex flex-col justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center\",\n                                    style: {\n                                        backgroundColor: iconColor\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        size: 20,\n                                        color: \"white\",\n                                        className: \"md:w-6 md:h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm md:text-base font-semibold\",\n                                            children: [\n                                                loanAmount,\n                                                \" \",\n                                                tokenTicker\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"$\",\n                                                loanValueUSDT.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center rounded-full p-1 border\",\n                            style: {\n                                borderColor: statusStyles.statusBorderColour\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 md:w-3 md:h-3 rounded-full\",\n                                style: {\n                                    backgroundColor: statusStyles.statusColour\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 md:mt-4 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground capitalize\",\n                            children: \"Margin call\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs md:text-sm font-semibold tracking-tight\",\n                            children: [\n                                \"$\",\n                                marginCallAmount.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_c = LoanCard;\nvar _c;\n$RefreshReg$(_c, \"LoanCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/LoanCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/MainDash.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/MainDash.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MainDash)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BellIcon_PlusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,PlusIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_PlusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,PlusIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_chart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/chart */ \"(app-pages-browser)/./src/components/ui/chart.tsx\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _NotificationPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NotificationPanel */ \"(app-pages-browser)/./src/components/dashboard/NotificationPanel.tsx\");\n/* harmony import */ var _LoanCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./LoanCard */ \"(app-pages-browser)/./src/components/dashboard/LoanCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst chartConfig = {\n    interest: {\n        label: \"Interest Earned\",\n        color: \"#10b981\"\n    }\n};\nfunction MainDash(param) {\n    let { interestData, loanData } = param;\n    _s();\n    const [isNotificationOpen, setIsNotificationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleNotifications = ()=>{\n        setIsNotificationOpen(!isNotificationOpen);\n    };\n    const closeNotifications = ()=>{\n        setIsNotificationOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full flex flex-col gap-4 p-3 md:p-4 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"flex-1 min-h-0 border-2 border-[#2F6FED]/70 shadow-sm rounded-3xl bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2 pt-4 px-4 md:px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm md:text-base font-semibold text-foreground pl-2 md:pl-3\",\n                                children: \"Interest earned all time\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 md:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                                        defaultValue: \"all-time\",\n                                        className: \"w-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                            className: \"grid grid-cols-3 gap-1 md:gap-2 bg-transparent p-0\",\n                                            children: [\n                                                \"all-time\",\n                                                \"yearly\",\n                                                \"6months\"\n                                            ].map((v)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                                    value: v,\n                                                    className: \"data-[state=active]:font-semibold data-[state=active]:text-[#2F6FED] text-muted-foreground px-2 md:px-3 py-1 rounded-full transition-colors text-xs md:text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-1 md:mr-2 capitalize\",\n                                                            children: v === \"all-time\" ? \"All time\" : v === \"6months\" ? \"6 Months\" : \"Yearly\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline-block\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-block size-2 rounded-full bg-transparent data-[state=active]:bg-[#2F6FED] translate-y-[-1px]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                                lineNumber: 58,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, v, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"icon\",\n                                                className: \"rounded-full border border-border h-8 w-8 md:h-9 md:w-9\",\n                                                onClick: toggleNotifications,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_PlusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 md:h-5 md:w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                isOpen: isNotificationOpen,\n                                                onClose: closeNotifications\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"px-2 pb-2 h-full min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full rounded-xl border border-none bg-muted/20 p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-full min-h-0 rounded-lg bg-background overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_5__.ChartContainer, {\n                                    config: chartConfig,\n                                    className: \"h-full w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.LineChart, {\n                                        data: interestData,\n                                        margin: {\n                                            top: 10,\n                                            right: 10,\n                                            left: 0,\n                                            bottom: 0\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.CartesianGrid, {\n                                                vertical: false,\n                                                stroke: \"#e5e7eb\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.XAxis, {\n                                                dataKey: \"month\",\n                                                tickLine: false,\n                                                axisLine: {\n                                                    stroke: '#d1d5db'\n                                                },\n                                                tickMargin: 8\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.YAxis, {\n                                                tickLine: false,\n                                                axisLine: {\n                                                    stroke: '#d1d5db'\n                                                },\n                                                tickFormatter: (val)=>\"$\".concat(val),\n                                                width: 60,\n                                                domain: [\n                                                    100,\n                                                    'auto'\n                                                ]\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_5__.ChartTooltip, {\n                                                cursor: {\n                                                    stroke: '#34d399',\n                                                    strokeOpacity: 0.15\n                                                },\n                                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_chart__WEBPACK_IMPORTED_MODULE_5__.ChartTooltipContent, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 30\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Line, {\n                                                type: \"monotone\",\n                                                dataKey: \"interest\",\n                                                stroke: chartConfig.interest.color,\n                                                strokeWidth: 3,\n                                                dot: false,\n                                                activeDot: {\n                                                    r: 4\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-base md:text-lg font-semibold text-foreground\",\n                        children: \"Loan insights\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        className: \"flex items-center font-semibold gap-2 rounded-full px-3 md:px-4 text-sm md:text-base h-8 md:h-9 bg-[#466DFF]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_PlusIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-3 w-3 md:h-4 md:w-4 font-bold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            \"Get loan\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 flex-1 min-h-0\",\n                children: loanData.map((loan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoanCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        id: loan.id,\n                        tokenTicker: loan.tokenTicker,\n                        loanAmount: loan.loanAmount,\n                        loanValueUSDT: loan.loanValueUSDT,\n                        status: loan.status,\n                        marginCallAmount: loan.marginCallAmount\n                    }, loan.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\MainDash.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(MainDash, \"tJLPVNQ2anz5HU04FnXudGjiWds=\");\n_c = MainDash;\nvar _c;\n$RefreshReg$(_c, \"MainDash\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MainDash.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/enums.ts":
/*!****************************!*\
  !*** ./src/types/enums.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoCurrency: () => (/* binding */ CryptoCurrency),\n/* harmony export */   LoanHealth: () => (/* binding */ LoanHealth),\n/* harmony export */   OnboardingSlide: () => (/* binding */ OnboardingSlide),\n/* harmony export */   SavingsCryptoCurrency: () => (/* binding */ SavingsCryptoCurrency),\n/* harmony export */   TimePeriod: () => (/* binding */ TimePeriod)\n/* harmony export */ });\n// Cryptocurrency types\nvar CryptoCurrency = /*#__PURE__*/ function(CryptoCurrency) {\n    CryptoCurrency[\"BTC\"] = \"BTC\";\n    CryptoCurrency[\"ETH\"] = \"ETH\";\n    CryptoCurrency[\"USDT\"] = \"USDT\";\n    return CryptoCurrency;\n}({});\n// Health status for loans\nvar LoanHealth = /*#__PURE__*/ function(LoanHealth) {\n    LoanHealth[\"GREEN\"] = \"Green\";\n    LoanHealth[\"YELLOW\"] = \"Yellow\";\n    LoanHealth[\"RED\"] = \"Red\";\n    return LoanHealth;\n}({});\n// Time period for charts\nvar TimePeriod = /*#__PURE__*/ function(TimePeriod) {\n    TimePeriod[\"ALL_TIME\"] = \"All time\";\n    TimePeriod[\"YEARLY\"] = \"Yearly\";\n    TimePeriod[\"SIX_MONTHS\"] = \"6 Months\";\n    return TimePeriod;\n}({});\n// Cryptocurrency types for savings\nvar SavingsCryptoCurrency = /*#__PURE__*/ function(SavingsCryptoCurrency) {\n    SavingsCryptoCurrency[\"BTC\"] = \"Bitcoin\";\n    SavingsCryptoCurrency[\"ETH\"] = \"Ethereum\";\n    SavingsCryptoCurrency[\"BNB\"] = \"BNB\";\n    SavingsCryptoCurrency[\"SOL\"] = \"Solana\";\n    SavingsCryptoCurrency[\"DASH\"] = \"DASH\";\n    SavingsCryptoCurrency[\"POLKADOT\"] = \"Polkadot\";\n    SavingsCryptoCurrency[\"MONERO\"] = \"Monero\";\n    return SavingsCryptoCurrency;\n}({});\n// Onboarding slide types\nvar OnboardingSlide = /*#__PURE__*/ function(OnboardingSlide) {\n    OnboardingSlide[\"INTRO\"] = \"intro\";\n    OnboardingSlide[\"BENEFITS\"] = \"benefits\";\n    OnboardingSlide[\"POTENTIAL\"] = \"potential\";\n    return OnboardingSlide;\n}({});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy90eXBlcy9lbnVtcy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBLHVCQUF1QjtBQUNoQiw0Q0FBS0E7Ozs7V0FBQUE7TUFJWDtBQUVELDBCQUEwQjtBQUNuQix3Q0FBS0M7Ozs7V0FBQUE7TUFJWDtBQUVELHlCQUF5QjtBQUNsQix3Q0FBS0M7Ozs7V0FBQUE7TUFJWDtBQUVELG1DQUFtQztBQUM1QixtREFBS0M7Ozs7Ozs7O1dBQUFBO01BUVg7QUFFRCx5QkFBeUI7QUFDbEIsNkNBQUtDOzs7O1dBQUFBO01BSVgiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29sb21cXERvY3VtZW50c1xcMDAxV29ya1Byb2plY3RcXGxlbmRibG9jXFx1c2VyXFxzcmNcXHR5cGVzXFxlbnVtcy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDcnlwdG9jdXJyZW5jeSB0eXBlc1xuZXhwb3J0IGVudW0gQ3J5cHRvQ3VycmVuY3kge1xuICBCVEMgPSAnQlRDJyxcbiAgRVRIID0gJ0VUSCcsXG4gIFVTRFQgPSAnVVNEVCdcbn1cblxuLy8gSGVhbHRoIHN0YXR1cyBmb3IgbG9hbnNcbmV4cG9ydCBlbnVtIExvYW5IZWFsdGgge1xuICBHUkVFTiA9ICdHcmVlbicsXG4gIFlFTExPVyA9ICdZZWxsb3cnLFxuICBSRUQgPSAnUmVkJ1xufVxuXG4vLyBUaW1lIHBlcmlvZCBmb3IgY2hhcnRzXG5leHBvcnQgZW51bSBUaW1lUGVyaW9kIHtcbiAgQUxMX1RJTUUgPSAnQWxsIHRpbWUnLFxuICBZRUFSTFkgPSAnWWVhcmx5JyxcbiAgU0lYX01PTlRIUyA9ICc2IE1vbnRocydcbn1cblxuLy8gQ3J5cHRvY3VycmVuY3kgdHlwZXMgZm9yIHNhdmluZ3NcbmV4cG9ydCBlbnVtIFNhdmluZ3NDcnlwdG9DdXJyZW5jeSB7XG4gIEJUQyA9ICdCaXRjb2luJyxcbiAgRVRIID0gJ0V0aGVyZXVtJyxcbiAgQk5CID0gJ0JOQicsXG4gIFNPTCA9ICdTb2xhbmEnLFxuICBEQVNIID0gJ0RBU0gnLFxuICBQT0xLQURPVCA9ICdQb2xrYWRvdCcsXG4gIE1PTkVSTyA9ICdNb25lcm8nXG59XG5cbi8vIE9uYm9hcmRpbmcgc2xpZGUgdHlwZXNcbmV4cG9ydCBlbnVtIE9uYm9hcmRpbmdTbGlkZSB7XG4gIElOVFJPID0gJ2ludHJvJyxcbiAgQkVORUZJVFMgPSAnYmVuZWZpdHMnLFxuICBQT1RFTlRJQUwgPSAncG90ZW50aWFsJ1xufSJdLCJuYW1lcyI6WyJDcnlwdG9DdXJyZW5jeSIsIkxvYW5IZWFsdGgiLCJUaW1lUGVyaW9kIiwiU2F2aW5nc0NyeXB0b0N1cnJlbmN5IiwiT25ib2FyZGluZ1NsaWRlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/enums.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/statusStyles.ts":
/*!***********************************!*\
  !*** ./src/utils/statusStyles.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STATUS_STYLES: () => (/* binding */ STATUS_STYLES),\n/* harmony export */   getStatusFromLoanHealth: () => (/* binding */ getStatusFromLoanHealth),\n/* harmony export */   getStatusFromScore: () => (/* binding */ getStatusFromScore),\n/* harmony export */   getStatusStyles: () => (/* binding */ getStatusStyles),\n/* harmony export */   statusFromScoreWithStyles: () => (/* binding */ statusFromScoreWithStyles)\n/* harmony export */ });\n// Status styles helper\n// Provides three status variants and helpers to select styles.\n/**\n * Hex color based style map for three status variants.\n * Use these values directly as `style` properties in components.\n */ const STATUS_STYLES = {\n    good: {\n        statusColour: \"#19D539\",\n        statusBorderColour: \"#FFFFFF\",\n        cardBG: \"#FFFFFF\",\n        cardBorderColour: \"#FFFFFF\"\n    },\n    warning: {\n        statusColour: \"#F6A70A\",\n        statusBorderColour: \"#F6A70A\",\n        cardBG: \"#FFF7E6\",\n        cardBorderColour: \"#F6A70A\"\n    },\n    danger: {\n        statusColour: \"#FF5353\",\n        statusBorderColour: \"#FF5353\",\n        cardBG: \"#FFF2F2\",\n        cardBorderColour: \"#FF5353\"\n    }\n};\n/**\n * Return the style object for a given variant. Defaults to `warning` if unknown.\n */ function getStatusStyles(variant) {\n    if (!variant || !(variant in STATUS_STYLES)) return STATUS_STYLES.warning;\n    return STATUS_STYLES[variant];\n}\n/**\n * Simple heuristic to map a numeric score (0-100) to a status variant.\n * - >= 80 => good\n * - >= 50 => warning\n * - < 50 => danger\n * Useful when backend returns a score/health metric and you want a visual status.\n */ function getStatusFromScore(score) {\n    if (typeof score !== \"number\" || Number.isNaN(score)) return \"warning\";\n    if (score >= 80) return \"good\";\n    if (score >= 50) return \"warning\";\n    return \"danger\";\n}\n/**\n * Map LoanHealth enum to StatusVariant\n */ function getStatusFromLoanHealth(health) {\n    switch(health.toLowerCase()){\n        case 'green':\n            return 'good';\n        case 'yellow':\n            return 'warning';\n        case 'red':\n            return 'danger';\n        default:\n            return 'warning';\n    }\n}\n/**\n * Convenience: return both variant and styles from a score\n */ function statusFromScoreWithStyles(score) {\n    const variant = getStatusFromScore(score !== null && score !== void 0 ? score : null);\n    return {\n        variant,\n        styles: getStatusStyles(variant)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusStyles.ts\n"));

/***/ })

});