"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/data/loanDashboardMockData.ts":
/*!*******************************************!*\
  !*** ./src/data/loanDashboardMockData.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockInterestData: () => (/* binding */ mockInterestData),\n/* harmony export */   mockLoanData: () => (/* binding */ mockLoanData),\n/* harmony export */   mockRootProps: () => (/* binding */ mockRootProps)\n/* harmony export */ });\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n\n// Mock data for the interest chart\nconst mockInterestData = [\n    {\n        month: 'Jan',\n        interest: 520\n    },\n    {\n        month: 'Feb',\n        interest: 480\n    },\n    {\n        month: 'Mar',\n        interest: 1590\n    },\n    {\n        month: 'Apr',\n        interest: 510\n    },\n    {\n        month: 'May',\n        interest: 2620\n    },\n    {\n        month: 'Jun',\n        interest: 580\n    },\n    {\n        month: 'Jul',\n        interest: 650\n    },\n    {\n        month: 'Aug',\n        interest: 1720\n    }\n];\n// Mock data for loan cards\nconst mockLoanData = [\n    {\n        id: '1',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n        loanAmount: 0.0025,\n        loanValueUSDT: 250.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n        marginCallAmount: 90568.21\n    },\n    {\n        id: '2',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n        loanAmount: 0.0090,\n        loanValueUSDT: 20.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.YELLOW,\n        marginCallAmount: 1800.36\n    },\n    {\n        id: '3',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n        loanAmount: 0.0025,\n        loanValueUSDT: 250.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.RED,\n        marginCallAmount: 90568.21\n    },\n    {\n        id: '4',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT,\n        loanAmount: 1000,\n        loanValueUSDT: 1000.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n        marginCallAmount: 950.00\n    }\n];\n// Data passed as props to the root component\nconst mockRootProps = {\n    interestData: mockInterestData,\n    loanData: mockLoanData,\n    loans: [\n        {\n            id: '1',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: true\n        },\n        {\n            id: '2',\n            collateralAmount: 0.0090,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n            collateralValueUSD: 20,\n            currentRate: 2477.21,\n            marginCall: 1800.36,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '3',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '4',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '5',\n            collateralAmount: 0.0090,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n            collateralValueUSD: 20,\n            currentRate: 2477.21,\n            marginCall: 1800.36,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        }\n    ],\n    portfolioInsights: {\n        totalValue: 2154.00,\n        interestEarnedData: [\n            {\n                month: 'Jan',\n                value: 520\n            },\n            {\n                month: 'Feb',\n                value: 480\n            },\n            {\n                month: 'Mar',\n                value: 590\n            },\n            {\n                month: 'Apr',\n                value: 510\n            },\n            {\n                month: 'May',\n                value: 620\n            },\n            {\n                month: 'Jun',\n                value: 580\n            },\n            {\n                month: 'Jul',\n                value: 650\n            },\n            {\n                month: 'Aug',\n                value: 1720\n            }\n        ],\n        holdings: [\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n                name: 'Bitcoin',\n                value: 18365.12,\n                change: 2.45,\n                isPositive: true\n            },\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n                name: 'Ethereum',\n                value: 13365.12,\n                change: 1.85,\n                isPositive: true\n            },\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT,\n                name: 'Tether',\n                value: 203.00,\n                change: -0.12,\n                isPositive: false\n            }\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/loanDashboardMockData.ts\n"));

/***/ })

});