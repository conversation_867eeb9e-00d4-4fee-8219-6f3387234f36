"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/FirstLandingDash */ \"(app-pages-browser)/./src/components/dashboard/FirstLandingDash.tsx\");\n/* harmony import */ var _components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/TopTrendsTable */ \"(app-pages-browser)/./src/components/dashboard/TopTrendsTable.tsx\");\n/* harmony import */ var _components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/NewListedCoins */ \"(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/MainDash */ \"(app-pages-browser)/./src/components/dashboard/MainDash.tsx\");\n/* harmony import */ var _components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/AccountTabContent */ \"(app-pages-browser)/./src/components/dashboard/AccountTabContent.tsx\");\n/* harmony import */ var _components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransactionsTabContent */ \"(app-pages-browser)/./src/components/dashboard/TransactionsTabContent.tsx\");\n/* harmony import */ var _lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/dashboard-data */ \"(app-pages-browser)/./src/lib/dashboard-data.ts\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardHomePage() {\n    _s();\n    const { isMobile } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMainDash, setShowMainDash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceToggle, setForceToggle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch dashboard data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardHomePage.useEffect\": ()=>{\n            const loadDashboardData = {\n                \"DashboardHomePage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__.fetchDashboardData)();\n                        setDashboardData(data);\n                        setShowMainDash(data.hasUserData);\n                    } catch (error) {\n                        console.error('Failed to load dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardHomePage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardHomePage.useEffect\"], []);\n    // Handle toggle between dashboards\n    const handleToggleDashboard = ()=>{\n        setForceToggle(!forceToggle);\n        setShowMainDash(!showMainDash);\n    };\n    var _dashboardData_hasUserData;\n    // Determine which dashboard to show\n    const shouldShowMainDash = forceToggle ? showMainDash : (_dashboardData_hasUserData = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.hasUserData) !== null && _dashboardData_hasUserData !== void 0 ? _dashboardData_hasUserData : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this);\n    }\n    if (!dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Failed to load dashboard data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"dashboard-toggle\",\n                                    className: \"text-sm font-medium\",\n                                    children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    id: \"dashboard-toggle\",\n                                    checked: shouldShowMainDash,\n                                    onCheckedChange: handleToggleDashboard\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                            loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ltvChartData: dashboardData.ltvChartData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 mt-4 min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"updates\",\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 mb-4 bg-gray-100 p-1 rounded-full h-12 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"updates\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"account\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"transactions\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-0 overflow-y-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"updates\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            trends: dashboardData.topTrendsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            coins: dashboardData.newCoinsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"account\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"transactions\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full grid grid-cols-1 gap-4 lg:grid-cols-[6fr_4fr] 2xl:grid-cols-[7fr_3fr]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"dashboard-toggle-desktop\",\n                                        className: \"text-sm font-medium\",\n                                        children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                        id: \"dashboard-toggle-desktop\",\n                                        checked: shouldShowMainDash,\n                                        onCheckedChange: handleToggleDashboard\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0\",\n                            children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                interestData: dashboardData.interestData,\n                                loanData: dashboardData.loanData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ltvChartData: dashboardData.ltvChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"h-full w-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            defaultValue: \"updates\",\n                            className: \"h-full flex flex-col bg-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid w-full grid-cols-3 mb-4 p-1 bg-transparent rounded-full h-12 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"updates\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"account\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"transactions\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-h-full min-w-full overflow-y-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"updates\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        trends: dashboardData.topTrendsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        coins: dashboardData.newCoinsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"account\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"transactions\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardHomePage, \"JIBGPP0NwMrIF0bqKkCUv4RQCLM=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType\n    ];\n});\n_c = DashboardHomePage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/data/loanDashboardMockData.ts":
/*!*******************************************!*\
  !*** ./src/data/loanDashboardMockData.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockInterestData: () => (/* binding */ mockInterestData),\n/* harmony export */   mockLoanData: () => (/* binding */ mockLoanData),\n/* harmony export */   mockRootProps: () => (/* binding */ mockRootProps)\n/* harmony export */ });\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n\n// Mock data for the interest chart\nconst mockInterestData = [\n    {\n        month: 'Jan',\n        interest: 520\n    },\n    {\n        month: 'Feb',\n        interest: 480\n    },\n    {\n        month: 'Mar',\n        interest: 590\n    },\n    {\n        month: 'Apr',\n        interest: 510\n    },\n    {\n        month: 'May',\n        interest: 620\n    },\n    {\n        month: 'Jun',\n        interest: 580\n    },\n    {\n        month: 'Jul',\n        interest: 650\n    },\n    {\n        month: 'Aug',\n        interest: 720\n    }\n];\n// Mock data for loan cards\nconst mockLoanData = [\n    {\n        id: '1',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n        loanAmount: 0.0025,\n        loanValueUSDT: 250.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n        marginCallAmount: 90568.21\n    },\n    {\n        id: '2',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n        loanAmount: 0.0090,\n        loanValueUSDT: 20.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.YELLOW,\n        marginCallAmount: 1800.36\n    },\n    {\n        id: '3',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n        loanAmount: 0.0025,\n        loanValueUSDT: 250.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.RED,\n        marginCallAmount: 90568.21\n    },\n    {\n        id: '4',\n        tokenTicker: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT,\n        loanAmount: 1000,\n        loanValueUSDT: 1000.00,\n        status: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n        marginCallAmount: 950.00\n    }\n];\n// Data passed as props to the root component\nconst mockRootProps = {\n    interestData: mockInterestData,\n    loanData: mockLoanData,\n    loans: [\n        {\n            id: '1',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: true\n        },\n        {\n            id: '2',\n            collateralAmount: 0.0090,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n            collateralValueUSD: 20,\n            currentRate: 2477.21,\n            marginCall: 1800.36,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '3',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '4',\n            collateralAmount: 0.0025,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n            collateralValueUSD: 250,\n            currentRate: 97568.21,\n            marginCall: 90568.21,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        },\n        {\n            id: '5',\n            collateralAmount: 0.0090,\n            collateralCurrency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n            collateralValueUSD: 20,\n            currentRate: 2477.21,\n            marginCall: 1800.36,\n            ratePair: \"\".concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH, \"/\").concat(_types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT),\n            health: _types_enums__WEBPACK_IMPORTED_MODULE_0__.LoanHealth.GREEN,\n            isHighlighted: false\n        }\n    ],\n    portfolioInsights: {\n        totalValue: 2154.00,\n        interestEarnedData: [\n            {\n                month: 'Jan',\n                value: 520\n            },\n            {\n                month: 'Feb',\n                value: 480\n            },\n            {\n                month: 'Mar',\n                value: 590\n            },\n            {\n                month: 'Apr',\n                value: 510\n            },\n            {\n                month: 'May',\n                value: 620\n            },\n            {\n                month: 'Jun',\n                value: 580\n            },\n            {\n                month: 'Jul',\n                value: 650\n            },\n            {\n                month: 'Aug',\n                value: 720\n            }\n        ],\n        holdings: [\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.BTC,\n                name: 'Bitcoin',\n                value: 18365.12,\n                change: 2.45,\n                isPositive: true\n            },\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.ETH,\n                name: 'Ethereum',\n                value: 13365.12,\n                change: 1.85,\n                isPositive: true\n            },\n            {\n                currency: _types_enums__WEBPACK_IMPORTED_MODULE_0__.CryptoCurrency.USDT,\n                name: 'Tether',\n                value: 203.00,\n                change: -0.12,\n                isPositive: false\n            }\n        ]\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/data/loanDashboardMockData.ts\n"));

/***/ })

});