/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/account/page";
exports.ids = ["app/account/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Faccount%2Fpage&page=%2Faccount%2Fpage&appPaths=%2Faccount%2Fpage&pagePath=private-next-app-dir%2Faccount%2Fpage.tsx&appDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Faccount%2Fpage&page=%2Faccount%2Fpage&appPaths=%2Faccount%2Fpage&pagePath=private-next-app-dir%2Faccount%2Fpage.tsx&appDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/account/page.tsx */ \"(rsc)/./src/app/account/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'account',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/account/page\",\n        pathname: \"/account\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \"dist\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/account/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Faccount%2Fpage&page=%2Faccount%2Fpage&appPaths=%2Faccount%2Fpage&pagePath=private-next-app-dir%2Faccount%2Fpage.tsx&appDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/account/page.tsx */ \"(rsc)/./src/app/account/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NvbG9tJTVDJTVDRG9jdW1lbnRzJTVDJTVDMDAxV29ya1Byb2plY3QlNUMlNUNsZW5kYmxvYyU1QyU1Q3VzZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhY2NvdW50JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29sb21cXFxcRG9jdW1lbnRzXFxcXDAwMVdvcmtQcm9qZWN0XFxcXGxlbmRibG9jXFxcXHVzZXJcXFxcc3JjXFxcXGFwcFxcXFxhY2NvdW50XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NvbG9tJTVDJTVDRG9jdW1lbnRzJTVDJTVDMDAxV29ya1Byb2plY3QlNUMlNUNsZW5kYmxvYyU1QyU1Q3VzZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNvbG9tXFxcXERvY3VtZW50c1xcXFwwMDFXb3JrUHJvamVjdFxcXFxsZW5kYmxvY1xcXFx1c2VyXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29sb21cXERvY3VtZW50c1xcMDAxV29ya1Byb2plY3RcXGxlbmRibG9jXFx1c2VyXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/account/page.tsx":
/*!**********************************!*\
  !*** ./src/app/account/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\001WorkProject\\lendbloc\\user\\src\\app\\account\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\001WorkProject\\lendbloc\\user\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/account/page.tsx */ \"(ssr)/./src/app/account/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NvbG9tJTVDJTVDRG9jdW1lbnRzJTVDJTVDMDAxV29ya1Byb2plY3QlNUMlNUNsZW5kYmxvYyU1QyU1Q3VzZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhY2NvdW50JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcc29sb21cXFxcRG9jdW1lbnRzXFxcXDAwMVdvcmtQcm9qZWN0XFxcXGxlbmRibG9jXFxcXHVzZXJcXFxcc3JjXFxcXGFwcFxcXFxhY2NvdW50XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Caccount%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NvbG9tJTVDJTVDRG9jdW1lbnRzJTVDJTVDMDAxV29ya1Byb2plY3QlNUMlNUNsZW5kYmxvYyU1QyU1Q3VzZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNvbG9tXFxcXERvY3VtZW50c1xcXFwwMDFXb3JrUHJvamVjdFxcXFxsZW5kYmxvY1xcXFx1c2VyXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Csolom%5C%5CDocuments%5C%5C001WorkProject%5C%5Clendbloc%5C%5Cuser%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/account/page.tsx":
/*!**********************************!*\
  !*** ./src/app/account/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AccountPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(ssr)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_account_ProfileHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/account/ProfileHeader */ \"(ssr)/./src/components/account/ProfileHeader.tsx\");\n/* harmony import */ var _components_account_PrivacySettingsCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/account/PrivacySettingsCard */ \"(ssr)/./src/components/account/PrivacySettingsCard.tsx\");\n/* harmony import */ var _components_account_AdditionalSettingsCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/account/AdditionalSettingsCard */ \"(ssr)/./src/components/account/AdditionalSettingsCard.tsx\");\n/* harmony import */ var _app_accountMockData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/accountMockData */ \"(ssr)/./src/app/accountMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AccountPage() {\n    const { isMobile } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_2__.useViewportType)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_app_accountMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.user);\n    const [privacySettings, setPrivacySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_app_accountMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.privacySettings);\n    const [appSettings, setAppSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_app_accountMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.appSettings);\n    const handleNicknameChange = (nickname)=>{\n        setUser((prev)=>({\n                ...prev,\n                nickname\n            }));\n    };\n    const handlePrivacySettingsChange = (newSettings)=>{\n        setPrivacySettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n    };\n    const handleAppSettingsChange = (newSettings)=>{\n        setAppSettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n    };\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex flex-col gap-4 overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_ProfileHeader__WEBPACK_IMPORTED_MODULE_3__.ProfileHeader, {\n                            user: user,\n                            onNicknameChange: handleNicknameChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_PrivacySettingsCard__WEBPACK_IMPORTED_MODULE_4__.PrivacySettingsCard, {\n                            settings: privacySettings,\n                            onSettingsChange: handlePrivacySettingsChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_AdditionalSettingsCard__WEBPACK_IMPORTED_MODULE_5__.AdditionalSettingsCard, {\n                            appSettings: appSettings,\n                            kycStatus: _app_accountMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.kycStatus,\n                            onAppSettingsChange: handleAppSettingsChange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full grid grid-cols-1 gap-4 lg:grid-cols-[6fr_4fr] 2xl:grid-cols-[7fr_3fr]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 flex flex-col gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_ProfileHeader__WEBPACK_IMPORTED_MODULE_3__.ProfileHeader, {\n                                user: user,\n                                onNicknameChange: handleNicknameChange\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_PrivacySettingsCard__WEBPACK_IMPORTED_MODULE_4__.PrivacySettingsCard, {\n                                settings: privacySettings,\n                                onSettingsChange: handlePrivacySettingsChange\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_account_AdditionalSettingsCard__WEBPACK_IMPORTED_MODULE_5__.AdditionalSettingsCard, {\n                        appSettings: appSettings,\n                        kycStatus: _app_accountMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.kycStatus,\n                        onAppSettingsChange: handleAppSettingsChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\account\\\\page.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/account/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/accountMockData.ts":
/*!************************************!*\
  !*** ./src/app/accountMockData.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockRootProps: () => (/* binding */ mockRootProps)\n/* harmony export */ });\n// Mock data for the account page\nconst mockRootProps = {\n    user: {\n        name: \"Rafael Lebowski\",\n        nickname: \"Rafael Lebowski\",\n        avatar: \"https://i.pravatar.cc/150?img=1\"\n    },\n    privacySettings: {\n        showAssetsValue: true,\n        twoFactorAuth: true\n    },\n    appSettings: {\n        displayMode: \"Clean\",\n        language: \"English\"\n    },\n    kycStatus: {\n        nationalIdVerified: true,\n        faceRecognitionApproved: true,\n        addressVerified: true\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2FjY291bnRNb2NrRGF0YS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUNBQWlDO0FBQzFCLE1BQU1BLGdCQUFnQjtJQUMzQkMsTUFBTTtRQUNKQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsUUFBUTtJQUNWO0lBQ0FDLGlCQUFpQjtRQUNmQyxpQkFBaUI7UUFDakJDLGVBQWU7SUFDakI7SUFDQUMsYUFBYTtRQUNYQyxhQUFhO1FBQ2JDLFVBQVU7SUFDWjtJQUNBQyxXQUFXO1FBQ1RDLG9CQUFvQjtRQUNwQkMseUJBQXlCO1FBQ3pCQyxpQkFBaUI7SUFDbkI7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbG9tXFxEb2N1bWVudHNcXDAwMVdvcmtQcm9qZWN0XFxsZW5kYmxvY1xcdXNlclxcc3JjXFxhcHBcXGFjY291bnRNb2NrRGF0YS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBNb2NrIGRhdGEgZm9yIHRoZSBhY2NvdW50IHBhZ2VcbmV4cG9ydCBjb25zdCBtb2NrUm9vdFByb3BzID0ge1xuICB1c2VyOiB7XG4gICAgbmFtZTogXCJSYWZhZWwgTGVib3dza2lcIixcbiAgICBuaWNrbmFtZTogXCJSYWZhZWwgTGVib3dza2lcIixcbiAgICBhdmF0YXI6IFwiaHR0cHM6Ly9pLnByYXZhdGFyLmNjLzE1MD9pbWc9MVwiXG4gIH0sXG4gIHByaXZhY3lTZXR0aW5nczoge1xuICAgIHNob3dBc3NldHNWYWx1ZTogdHJ1ZSxcbiAgICB0d29GYWN0b3JBdXRoOiB0cnVlXG4gIH0sXG4gIGFwcFNldHRpbmdzOiB7XG4gICAgZGlzcGxheU1vZGU6IFwiQ2xlYW5cIixcbiAgICBsYW5ndWFnZTogXCJFbmdsaXNoXCJcbiAgfSxcbiAga3ljU3RhdHVzOiB7XG4gICAgbmF0aW9uYWxJZFZlcmlmaWVkOiB0cnVlLFxuICAgIGZhY2VSZWNvZ25pdGlvbkFwcHJvdmVkOiB0cnVlLFxuICAgIGFkZHJlc3NWZXJpZmllZDogdHJ1ZVxuICB9XG59OyJdLCJuYW1lcyI6WyJtb2NrUm9vdFByb3BzIiwidXNlciIsIm5hbWUiLCJuaWNrbmFtZSIsImF2YXRhciIsInByaXZhY3lTZXR0aW5ncyIsInNob3dBc3NldHNWYWx1ZSIsInR3b0ZhY3RvckF1dGgiLCJhcHBTZXR0aW5ncyIsImRpc3BsYXlNb2RlIiwibGFuZ3VhZ2UiLCJreWNTdGF0dXMiLCJuYXRpb25hbElkVmVyaWZpZWQiLCJmYWNlUmVjb2duaXRpb25BcHByb3ZlZCIsImFkZHJlc3NWZXJpZmllZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/accountMockData.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a0a1ef865568\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbG9tXFxEb2N1bWVudHNcXDAwMVdvcmtQcm9qZWN0XFxsZW5kYmxvY1xcdXNlclxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTBhMWVmODY1NTY4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* harmony import */ var _hooks_context_useAppContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/context/useAppContext */ \"(ssr)/./src/hooks/context/useAppContext.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(ssr)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_general__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/general */ \"(ssr)/./src/components/general/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n// export const metadata: Metadata = {\n//   title: \"Create Next App\",\n//   description: \"Generated by create next app\",\n// }\nconst ClientAuthGate = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\layout.tsx -> \" + \"./private/AuthGate\"\n        ]\n    },\n    ssr: false\n});\nfunction Shell({ children }) {\n    const { isMobile, isTablet } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_5__.useViewportType)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Hide sidebars and mobile navbar on /auth\n    const hideChrome = pathname === \"/auth\";\n    // Centralize responsive tokens to improve readability and maintainability.\n    // Prefer explicit branches to nested ternaries for clarity.\n    const layout = (()=>{\n        if (hideChrome) {\n            return {\n                containerClass: \"min-h-screen\",\n                sidebarVisible: false,\n                sidebarMinWidth: \"0\",\n                contentMinWidth: \"100vw\",\n                contentPaddingBottom: \"0px\",\n                showMobileNav: false\n            };\n        }\n        if (isMobile) {\n            return {\n                containerClass: \"min-h-screen\",\n                sidebarVisible: false,\n                sidebarMinWidth: \"0\",\n                contentMinWidth: \"100vw\",\n                // Reserve space for fixed mobile navbar height (e.g., 64px)\n                contentPaddingBottom: \"64px\",\n                showMobileNav: true\n            };\n        }\n        if (isTablet) {\n            return {\n                containerClass: \"min-h-screen flex\",\n                sidebarVisible: true,\n                sidebarMinWidth: \"20vw\",\n                contentMinWidth: \"80vw\",\n                contentPaddingBottom: \"0px\",\n                showMobileNav: false\n            };\n        }\n        // Desktop default\n        return {\n            containerClass: \"min-h-screen flex\",\n            sidebarVisible: true,\n            sidebarMinWidth: \"25vw\",\n            contentMinWidth: \"75vw\",\n            contentPaddingBottom: \"0px\",\n            showMobileNav: false\n        };\n    })();\n    // Memoize style objects to avoid re-renders in children that rely on shallow prop checks.\n    const sidebarStyle = react__WEBPACK_IMPORTED_MODULE_6___default().useMemo({\n        \"Shell.useMemo[sidebarStyle]\": ()=>({\n                minWidth: layout.sidebarMinWidth\n            })\n    }[\"Shell.useMemo[sidebarStyle]\"], [\n        layout.sidebarMinWidth\n    ]);\n    const contentStyle = react__WEBPACK_IMPORTED_MODULE_6___default().useMemo({\n        \"Shell.useMemo[contentStyle]\": ()=>({\n                minWidth: layout.contentMinWidth,\n                paddingBottom: layout.contentPaddingBottom\n            })\n    }[\"Shell.useMemo[contentStyle]\"], [\n        layout.contentMinWidth,\n        layout.contentPaddingBottom\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: layout.containerClass,\n        children: [\n            layout.sidebarVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: sidebarStyle,\n                \"aria-hidden\": false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_general__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 98,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"min-h-screen\",\n                style: contentStyle,\n                role: \"main\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            layout.showMobileNav && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed bottom-0 left-0 right-0 z-50 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/60 pb-4\",\n                \"aria-label\": \"Mobile navigation\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"flex items-center justify-around text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"i-lucide-home h-5 w-5\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/savings\",\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"i-lucide-piggy-bank h-5 w-5\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Savings\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/loan\",\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"i-lucide-banknote h-5 w-5\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Loan\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/account\",\n                                className: \"flex flex-col items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"i-lucide-user h-5 w-5\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Account\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_8___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_9___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_context_useAppContext__WEBPACK_IMPORTED_MODULE_2__.AppProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClientAuthGate, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Shell, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 155,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/account/AdditionalSettingsCard.tsx":
/*!***********************************************************!*\
  !*** ./src/components/account/AdditionalSettingsCard.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdditionalSettingsCard: () => (/* binding */ AdditionalSettingsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/switch */ \"(ssr)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CircleCheck,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CircleCheck,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* __next_internal_client_entry_do_not_use__ AdditionalSettingsCard auto */ \n\n\n\n\nfunction AdditionalSettingsCard({ appSettings, kycStatus, onAppSettingsChange }) {\n    const handleDisplayModeToggle = (checked)=>{\n        onAppSettingsChange?.({\n            displayMode: checked ? \"Clean\" : \"Standard\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"bg-white border border-gray-200 h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Additional settings\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            htmlFor: \"app-display\",\n                                            className: \"text-gray-700 font-medium\",\n                                            children: \"App display\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                                            id: \"app-display\",\n                                            checked: appSettings.displayMode === \"Clean\",\n                                            onCheckedChange: handleDisplayModeToggle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: appSettings.displayMode\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"Language\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: appSettings.language\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 pt-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            className: \"text-gray-700 font-medium\",\n                                            children: \"KYC information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: \"National identity number verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: \"Face recognition approved\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircleCheck_Info_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-700\",\n                                                    children: \"Address verified\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\AdditionalSettingsCard.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/account/AdditionalSettingsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/account/PrivacySettingsCard.tsx":
/*!********************************************************!*\
  !*** ./src/components/account/PrivacySettingsCard.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrivacySettingsCard: () => (/* binding */ PrivacySettingsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/switch */ \"(ssr)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ PrivacySettingsCard auto */ \n\n\n\n\nfunction PrivacySettingsCard({ settings, onSettingsChange }) {\n    const handleToggle = (key, value)=>{\n        onSettingsChange?.({\n            [key]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"bg-white border border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-6\",\n                    children: \"Privacy and Security\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    htmlFor: \"show-assets\",\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"Show assets value\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                                    id: \"show-assets\",\n                                    checked: settings.showAssetsValue,\n                                    onCheckedChange: (checked)=>handleToggle('showAssetsValue', checked)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    htmlFor: \"two-factor\",\n                                    className: \"text-gray-700 font-medium\",\n                                    children: \"2 Factor authentication\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                                    id: \"two-factor\",\n                                    checked: settings.twoFactorAuth,\n                                    onCheckedChange: (checked)=>handleToggle('twoFactorAuth', checked)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between py-2 cursor-pointer hover:bg-gray-50 -mx-2 px-2 rounded-md transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                    className: \"text-gray-700 font-medium cursor-pointer\",\n                                    children: \"Change email address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\PrivacySettingsCard.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/account/PrivacySettingsCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/account/ProfileHeader.tsx":
/*!**************************************************!*\
  !*** ./src/components/account/ProfileHeader.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileHeader: () => (/* binding */ ProfileHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ProfileHeader auto */ \n\n\n\n\nfunction ProfileHeader({ user, onNicknameChange }) {\n    const [nickname, setNickname] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(user.nickname);\n    const handleNicknameChange = (value)=>{\n        setNickname(value);\n        onNicknameChange?.(value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative shadow-2xl rounded-b-[5rem]\",\n                children: [\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"absolute z-5 bottom-0 rounded-b-full left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-gray-400/50 min-h-[3rem] min-w-[calc(100%-6.5rem)]\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"relative z-10 bg-blue-600 text-white border-0 rounded-t-none rounded-b-[5rem] overflow-visible\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 relative pb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"text-white hover:text-blue-100 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold\",\n                                                children: \"LENDBLOC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                    className: \"absolute z-50 bottom-5 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-40 h-40 border-4 border-white/20 z-10\",\n                                    children: [\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                            src: user.avatar,\n                                            alt: user.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                            className: \"bg-blue-500 text-white text-xl\",\n                                            children: user.name.split(' ').map((n)=>n[0]).join('')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex mt-12 justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-semibold text-center text-blue-600\",\n                        children: \"John Doe\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\account\\\\ProfileHeader.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/account/ProfileHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/general/Sidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/general/Sidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_general__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/general */ \"(ssr)/./src/components/general/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Logo = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center px-6 py-6 md:py-7 lg:py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: \"/images/logoLB.svg\",\n            alt: \"LENDBLOC Logo\",\n            className: \"h-6 w-auto md:h-5 lg:h-16\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\nconst items = [\n    {\n        href: \"/\",\n        label: \"Dashboard\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"22\",\n            height: \"22\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"3\",\n                    y: \"3\",\n                    width: \"8\",\n                    height: \"8\",\n                    rx: \"2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"13\",\n                    y: \"3\",\n                    width: \"8\",\n                    height: \"8\",\n                    rx: \"2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"3\",\n                    y: \"13\",\n                    width: \"8\",\n                    height: \"8\",\n                    rx: \"2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    x: \"13\",\n                    y: \"13\",\n                    width: \"8\",\n                    height: \"8\",\n                    rx: \"2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        href: \"/loan\",\n        label: \"Loan\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"22\",\n            height: \"22\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"9\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M8 13c1.5 1 6 1 8 0\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M9 9h6\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        href: \"/savings\",\n        label: \"Savings\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"22\",\n            height: \"22\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M5 12a7 7 0 0114 0v4H7a2 2 0 01-2-2v-2z\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"16.5\",\n                    cy: \"11\",\n                    r: \"1\",\n                    fill: \"white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M4 14h2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        href: \"/referral\",\n        label: \"Referral\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"22\",\n            height: \"22\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"7\",\n                    cy: \"8\",\n                    r: \"3\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"17\",\n                    cy: \"8\",\n                    r: \"3\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M4 18c.6-2.1 2.7-4 6-4s5.4 1.9 6 4\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    },\n    {\n        href: \"/account\",\n        label: \"Account\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            width: \"22\",\n            height: \"22\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"12\",\n                    cy: \"8\",\n                    r: \"3.2\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M5 19c0-3.3 3.1-6 7-6s7 2.7 7 6\",\n                    stroke: \"white\",\n                    strokeWidth: \"1.8\",\n                    strokeLinecap: \"round\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined)\n    }\n];\nconst Sidebar = ()=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"   hidden md:flex md:flex-col   w-full shrink-0 border-r border-slate-200   bg-white text-slate-700   h-screen sticky top-0   \",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                \"aria-label\": \"Lendbloc\",\n                className: \"select-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"mt-6 xl:mt-12 flex-1 xl:pl-6 pl-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"flex flex-col gap-18\",\n                    children: items.map((item)=>{\n                        const active = item.href === \"/\" ? pathname === \"/\" : pathname?.startsWith(item.href);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"pl-6 font-semibold\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_general__WEBPACK_IMPORTED_MODULE_4__.SidebarItem, {\n                                href: item.href,\n                                label: item.label,\n                                active: !!active,\n                                icon: item.icon,\n                                trailing: item.trailing\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.href, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\Sidebar.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/general/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/general/SidebarItem.tsx":
/*!************************************************!*\
  !*** ./src/components/general/SidebarItem.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst SidebarItem = ({ href, label, icon, trailing, active })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: href,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\"group flex items-center justify-between gap-3 xl:pr-6\", \"text-slate-600 hover:text-slate-900\", \"transition-colors\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center xl:gap-4 gap-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\"grid place-items-center rounded-full\", \"h-10 w-10\", active ? \"bg-[#2563EB]\" : \"bg-slate-700\"),\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(\"text-2xl\", active ? \"text-[#2563EB] font-semibold\" : \"text-slate-700\"),\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"opacity-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/images/active_arrow.svg\",\n                    alt: \"Active arrow\",\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined) : trailing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"opacity-100\",\n                children: trailing\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, undefined) : null\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\general\\\\SidebarItem.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SidebarItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/general/SidebarItem.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/general/index.ts":
/*!*****************************************!*\
  !*** ./src/components/general/index.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* reexport safe */ _Sidebar__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   SidebarItem: () => (/* reexport safe */ _SidebarItem__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/general/Sidebar.tsx\");\n/* harmony import */ var _SidebarItem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SidebarItem */ \"(ssr)/./src/components/general/SidebarItem.tsx\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9nZW5lcmFsL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0M7QUFDUSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb2xvbVxcRG9jdW1lbnRzXFwwMDFXb3JrUHJvamVjdFxcbGVuZGJsb2NcXHVzZXJcXHNyY1xcY29tcG9uZW50c1xcZ2VuZXJhbFxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBTaWRlYmFyIH0gZnJvbSBcIi4vU2lkZWJhclwiO1xyXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNpZGViYXJJdGVtIH0gZnJvbSBcIi4vU2lkZWJhckl0ZW1cIjsiXSwibmFtZXMiOlsiZGVmYXVsdCIsIlNpZGViYXIiLCJTaWRlYmFySXRlbSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/general/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarImage,AvatarFallback auto */ \n\n\n\nfunction Avatar({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"avatar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex size-8 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarImage({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        \"data-slot\": \"avatar-image\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square size-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\nfunction AvatarFallback({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        \"data-slot\": \"avatar-fallback\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-muted flex size-full items-center justify-center rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction Card({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\nfunction CardHeader({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\nfunction CardTitle({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\nfunction CardDescription({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CardAction({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\nfunction CardContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\nfunction CardFooter({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\nfunction Label({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFFdkI7QUFFaEMsU0FBU0csTUFBTSxFQUNiQyxTQUFTLEVBQ1QsR0FBR0MsT0FDOEM7SUFDakQscUJBQ0UsOERBQUNKLHVEQUFtQjtRQUNsQk0sYUFBVTtRQUNWSCxXQUFXRiw4Q0FBRUEsQ0FDWCx1TkFDQUU7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHZjtBQUVnQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb2xvbVxcRG9jdW1lbnRzXFwwMDFXb3JrUHJvamVjdFxcbGVuZGJsb2NcXHVzZXJcXHNyY1xcY29tcG9uZW50c1xcdWlcXGxhYmVsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZnVuY3Rpb24gTGFiZWwoe1xuICBjbGFzc05hbWUsXG4gIC4uLnByb3BzXG59OiBSZWFjdC5Db21wb25lbnRQcm9wczx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4pIHtcbiAgcmV0dXJuIChcbiAgICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgICAgZGF0YS1zbG90PVwibGFiZWxcIlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIGxlYWRpbmctbm9uZSBmb250LW1lZGl1bSBzZWxlY3Qtbm9uZSBncm91cC1kYXRhLVtkaXNhYmxlZD10cnVlXTpwb2ludGVyLWV2ZW50cy1ub25lIGdyb3VwLWRhdGEtW2Rpc2FibGVkPXRydWVdOm9wYWNpdHktNTAgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY24iLCJMYWJlbCIsImNsYXNzTmFtZSIsInByb3BzIiwiUm9vdCIsImRhdGEtc2xvdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/switch.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/switch.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: () => (/* binding */ Switch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-switch */ \"(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\n\nfunction Switch({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"switch\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n            \"data-slot\": \"switch-thumb\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\switch.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\ui\\\\switch.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/switch.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/context/useAppContext.tsx":
/*!*********************************************!*\
  !*** ./src/hooks/context/useAppContext.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider),\n/* harmony export */   useAppContext: () => (/* binding */ useAppContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _useAppState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useAppState */ \"(ssr)/./src/hooks/context/useAppState.tsx\");\n/* harmony import */ var _useAppStateManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useAppStateManager */ \"(ssr)/./src/hooks/context/useAppStateManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppProvider,useAppContext auto */ \n\n\n\n// Create a context holding the manager facade\nconst AppContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nfunction AppProvider({ children }) {\n    // Ensure the store is created (side-effect free)\n    (0,_useAppState__WEBPACK_IMPORTED_MODULE_2__.useAppState)();\n    const manager = (0,_useAppStateManager__WEBPACK_IMPORTED_MODULE_3__.useAppStateManager)();\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AppProvider.useMemo[value]\": ()=>manager\n    }[\"AppProvider.useMemo[value]\"], [\n        manager\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\hooks\\\\context\\\\useAppContext.tsx\",\n        lineNumber: 25,\n        columnNumber: 10\n    }, this);\n}\nfunction useAppContext() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext);\n    if (!ctx) {\n        throw new Error(\"useAppContext must be used within an AppProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/context/useAppContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/context/useAppState.tsx":
/*!*******************************************!*\
  !*** ./src/hooks/context/useAppState.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   selectors: () => (/* binding */ selectors),\n/* harmony export */   useAppState: () => (/* binding */ useAppState)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAppState,selectors auto */ \n\n/**\r\n * Initial state\r\n */ const initialState = {\n    // Auth\n    isAuthenticated: false,\n    accessToken: null,\n    refreshToken: null,\n    user: null,\n    // UI\n    theme: \"system\",\n    sidebarOpen: false,\n    loadingCount: 0,\n    lastToast: null,\n    // Dev\n    devMode: false\n};\n/**\r\n * Store creation with persist (localStorage)\r\n */ const useAppState = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        // Auth\n        login: ({ token, user, refreshToken })=>set(()=>({\n                    isAuthenticated: true,\n                    accessToken: token,\n                    refreshToken: refreshToken ?? null,\n                    user\n                })),\n        logout: ()=>set(()=>({\n                    ...initialState,\n                    theme: get().theme,\n                    devMode: get().devMode\n                })),\n        setAccessToken: (token)=>set(()=>({\n                    accessToken: token\n                })),\n        setUser: (user)=>set(()=>({\n                    user\n                })),\n        // UI\n        setTheme: (mode)=>set(()=>({\n                    theme: mode\n                })),\n        toggleSidebar: (open)=>set((state)=>({\n                    sidebarOpen: typeof open === \"boolean\" ? open : !state.sidebarOpen\n                })),\n        beginLoading: ()=>set((s)=>({\n                    loadingCount: Math.max(0, s.loadingCount + 1)\n                })),\n        endLoading: ()=>set((s)=>({\n                    loadingCount: Math.max(0, s.loadingCount - 1)\n                })),\n        toast: (type, message)=>set(()=>({\n                    lastToast: {\n                        type,\n                        message,\n                        ts: Date.now()\n                    }\n                })),\n        // Dev\n        setDevMode: (on)=>set(()=>({\n                    devMode: on\n                })),\n        devQuickLogin: ()=>{\n            const devUser = {\n                id: \"dev-123\",\n                name: \"Developer\",\n                email: \"<EMAIL>\",\n                role: \"developer\",\n                avatarUrl: null\n            };\n            get().login({\n                token: \"dev-token\",\n                refreshToken: \"dev-refresh\",\n                user: devUser\n            });\n            get().toast(\"success\", \"Logged in as Developer\");\n        },\n        devQuickLogout: ()=>{\n            get().logout();\n            get().toast(\"info\", \"Developer logged out\");\n        }\n    }), {\n    name: \"app-state\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=> false ? 0 : undefined),\n    // Do not persist volatile fields\n    partialize: (state)=>({\n            isAuthenticated: state.isAuthenticated,\n            accessToken: state.accessToken,\n            refreshToken: state.refreshToken,\n            user: state.user,\n            theme: state.theme,\n            sidebarOpen: state.sidebarOpen,\n            devMode: state.devMode\n        })\n}));\n/**\r\n * Selectors (ergonomic helpers)\r\n */ const selectors = {\n    auth: (s)=>({\n            isAuthenticated: s.isAuthenticated,\n            user: s.user,\n            accessToken: s.accessToken,\n            refreshToken: s.refreshToken\n        }),\n    ui: (s)=>({\n            theme: s.theme,\n            sidebarOpen: s.sidebarOpen,\n            loadingCount: s.loadingCount,\n            lastToast: s.lastToast\n        }),\n    dev: (s)=>({\n            devMode: s.devMode\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/context/useAppState.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/context/useAppStateManager.tsx":
/*!**************************************************!*\
  !*** ./src/hooks/context/useAppStateManager.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appSelectors: () => (/* binding */ appSelectors),\n/* harmony export */   useAppStateManager: () => (/* binding */ useAppStateManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useAppState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useAppState */ \"(ssr)/./src/hooks/context/useAppState.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppStateManager,appSelectors auto */ \n\n/**\r\n * A thin \"manager\" hook that exposes curated selectors and composed actions.\r\n * This helps keep components lean and encourages a single place for orchestration logic.\r\n */ function useAppStateManager() {\n    // Select slices to avoid unnecessary re-renders\n    const isAuthenticated = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[isAuthenticated]\": (s)=>s.isAuthenticated\n    }[\"useAppStateManager.useAppState[isAuthenticated]\"]);\n    const user = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[user]\": (s)=>s.user\n    }[\"useAppStateManager.useAppState[user]\"]);\n    const accessToken = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[accessToken]\": (s)=>s.accessToken\n    }[\"useAppStateManager.useAppState[accessToken]\"]);\n    const refreshToken = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[refreshToken]\": (s)=>s.refreshToken\n    }[\"useAppStateManager.useAppState[refreshToken]\"]);\n    const theme = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[theme]\": (s)=>s.theme\n    }[\"useAppStateManager.useAppState[theme]\"]);\n    const sidebarOpen = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[sidebarOpen]\": (s)=>s.sidebarOpen\n    }[\"useAppStateManager.useAppState[sidebarOpen]\"]);\n    const loadingCount = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[loadingCount]\": (s)=>s.loadingCount\n    }[\"useAppStateManager.useAppState[loadingCount]\"]);\n    const lastToast = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[lastToast]\": (s)=>s.lastToast\n    }[\"useAppStateManager.useAppState[lastToast]\"]);\n    const devMode = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[devMode]\": (s)=>s.devMode\n    }[\"useAppStateManager.useAppState[devMode]\"]);\n    // Actions\n    const login = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[login]\": (s)=>s.login\n    }[\"useAppStateManager.useAppState[login]\"]);\n    const logout = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[logout]\": (s)=>s.logout\n    }[\"useAppStateManager.useAppState[logout]\"]);\n    const setAccessToken = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[setAccessToken]\": (s)=>s.setAccessToken\n    }[\"useAppStateManager.useAppState[setAccessToken]\"]);\n    const setUser = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[setUser]\": (s)=>s.setUser\n    }[\"useAppStateManager.useAppState[setUser]\"]);\n    const setTheme = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[setTheme]\": (s)=>s.setTheme\n    }[\"useAppStateManager.useAppState[setTheme]\"]);\n    const toggleSidebar = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[toggleSidebar]\": (s)=>s.toggleSidebar\n    }[\"useAppStateManager.useAppState[toggleSidebar]\"]);\n    const beginLoading = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[beginLoading]\": (s)=>s.beginLoading\n    }[\"useAppStateManager.useAppState[beginLoading]\"]);\n    const endLoading = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[endLoading]\": (s)=>s.endLoading\n    }[\"useAppStateManager.useAppState[endLoading]\"]);\n    const toast = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[toast]\": (s)=>s.toast\n    }[\"useAppStateManager.useAppState[toast]\"]);\n    const setDevMode = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[setDevMode]\": (s)=>s.setDevMode\n    }[\"useAppStateManager.useAppState[setDevMode]\"]);\n    const devQuickLogin = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[devQuickLogin]\": (s)=>s.devQuickLogin\n    }[\"useAppStateManager.useAppState[devQuickLogin]\"]);\n    const devQuickLogout = (0,_useAppState__WEBPACK_IMPORTED_MODULE_1__.useAppState)({\n        \"useAppStateManager.useAppState[devQuickLogout]\": (s)=>s.devQuickLogout\n    }[\"useAppStateManager.useAppState[devQuickLogout]\"]);\n    // Composed helpers\n    // Avoid generic directly in TSX context by declaring function without generic, then casting\n    const _withGlobalLoading = async (work, onError)=>{\n        beginLoading();\n        try {\n            const res = await work();\n            return res;\n        } catch (e) {\n            onError?.(e);\n            toast(\"error\", e?.message ?? \"An error occurred\");\n            return undefined;\n        } finally{\n            endLoading();\n        }\n    };\n    const withGlobalLoading = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_withGlobalLoading, [\n        beginLoading,\n        endLoading,\n        toast\n    ]);\n    const manager = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useAppStateManager.useMemo[manager]\": ()=>({\n                // state\n                isAuthenticated,\n                user,\n                accessToken,\n                refreshToken,\n                theme,\n                sidebarOpen,\n                loadingCount,\n                lastToast,\n                devMode,\n                // basic actions\n                login,\n                logout,\n                setAccessToken,\n                setUser,\n                setTheme,\n                toggleSidebar,\n                beginLoading,\n                endLoading,\n                toast,\n                setDevMode,\n                devQuickLogin,\n                devQuickLogout,\n                // composed actions\n                withGlobalLoading,\n                // convenience\n                requireAuth: ({\n                    \"useAppStateManager.useMemo[manager]\": (message = \"Authentication required\")=>{\n                        if (!isAuthenticated) {\n                            toast(\"error\", message);\n                            return false;\n                        }\n                        return true;\n                    }\n                })[\"useAppStateManager.useMemo[manager]\"],\n                switchTheme: ({\n                    \"useAppStateManager.useMemo[manager]\": (next)=>{\n                        if (next) {\n                            setTheme(next);\n                            return;\n                        }\n                        setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n                    }\n                })[\"useAppStateManager.useMemo[manager]\"],\n                updateUserName: ({\n                    \"useAppStateManager.useMemo[manager]\": (name)=>{\n                        if (!user) return;\n                        const updated = {\n                            ...user,\n                            name\n                        };\n                        setUser(updated);\n                    }\n                })[\"useAppStateManager.useMemo[manager]\"]\n            })\n    }[\"useAppStateManager.useMemo[manager]\"], [\n        // state\n        isAuthenticated,\n        user,\n        accessToken,\n        refreshToken,\n        theme,\n        sidebarOpen,\n        loadingCount,\n        lastToast,\n        devMode,\n        // actions\n        login,\n        logout,\n        setAccessToken,\n        setUser,\n        setTheme,\n        toggleSidebar,\n        beginLoading,\n        endLoading,\n        toast,\n        setDevMode,\n        devQuickLogin,\n        devQuickLogout,\n        // composed\n        withGlobalLoading\n    ]);\n    return manager;\n}\nconst appSelectors = _useAppState__WEBPACK_IMPORTED_MODULE_1__.selectors;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/context/useAppStateManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/use-viewport.ts":
/*!***********************************!*\
  !*** ./src/hooks/use-viewport.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useViewportType: () => (/* binding */ useViewportType)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_viewport__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/viewport */ \"(ssr)/./src/lib/viewport.ts\");\n/* __next_internal_client_entry_do_not_use__ useViewportType auto */ \n\n/**\r\n * React hook that returns the current viewport type and convenience booleans.\r\n * - mobile: < 768\r\n * - tablet: 768–1023\r\n * - desktop: ≥ 1024\r\n *\r\n * Uses ResizeObserver when available; falls back to window resize event.\r\n * Safe for SSR: initializes to 'desktop' until hydrated on client.\r\n */ function useViewportType() {\n    const [type, setType] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        \"useViewportType.useState\": ()=>(0,_lib_viewport__WEBPACK_IMPORTED_MODULE_1__.getCurrentViewportType)()\n    }[\"useViewportType.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useViewportType.useEffect\": ()=>{\n            // Hydrate to accurate value on mount\n            const update = {\n                \"useViewportType.useEffect.update\": ()=>{\n                    setType((0,_lib_viewport__WEBPACK_IMPORTED_MODULE_1__.getViewportType)(window.innerWidth));\n                }\n            }[\"useViewportType.useEffect.update\"];\n            // Prefer ResizeObserver for better responsiveness on zoom/UA changes\n            let ro = null;\n            if (typeof ResizeObserver !== 'undefined') {\n                ro = new ResizeObserver(update);\n                ro.observe(document.documentElement);\n            } else {\n                window.addEventListener('resize', update);\n            }\n            // Initial compute\n            update();\n            return ({\n                \"useViewportType.useEffect\": ()=>{\n                    if (ro) {\n                        ro.disconnect();\n                    } else {\n                        window.removeEventListener('resize', update);\n                    }\n                }\n            })[\"useViewportType.useEffect\"];\n        }\n    }[\"useViewportType.useEffect\"], []);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useViewportType.useMemo\": ()=>{\n            return {\n                type,\n                isMobile: type === 'mobile',\n                isTablet: type === 'tablet',\n                isDesktop: type === 'desktop'\n            };\n        }\n    }[\"useViewportType.useMemo\"], [\n        type\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/use-viewport.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb2xvbVxcRG9jdW1lbnRzXFwwMDFXb3JrUHJvamVjdFxcbGVuZGJsb2NcXHVzZXJcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/viewport.ts":
/*!*****************************!*\
  !*** ./src/lib/viewport.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentViewportFlags: () => (/* binding */ getCurrentViewportFlags),\n/* harmony export */   getCurrentViewportType: () => (/* binding */ getCurrentViewportType),\n/* harmony export */   getViewportType: () => (/* binding */ getViewportType),\n/* harmony export */   isDesktop: () => (/* binding */ isDesktop),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isTablet: () => (/* binding */ isTablet)\n/* harmony export */ });\n/**\r\n * Determine viewport type from a given width.\r\n * Breakpoints:\r\n * - mobile: < 768\r\n * - tablet: 768 - 1023\r\n * - desktop: ≥ 1024\r\n */ function getViewportType(width) {\n    if (width < 768) return 'mobile';\n    if (width < 1200) return 'tablet';\n    return 'desktop';\n}\n/**\r\n * Get viewport type from current window.innerWidth.\r\n * Safe on server: returns 'desktop' as a sensible default.\r\n */ function getCurrentViewportType() {\n    if (true) return 'desktop';\n    return getViewportType(window.innerWidth);\n}\nfunction isMobile(width) {\n    return width < 768;\n}\nfunction isTablet(width) {\n    return width >= 768 && width < 1200;\n}\nfunction isDesktop(width) {\n    return width >= 1200;\n}\n/**\r\n * Convenience flags from current window width.\r\n * Safe on server: desktop defaults.\r\n */ function getCurrentViewportFlags() {\n    const type = getCurrentViewportType();\n    return {\n        type,\n        isMobile: type === 'mobile',\n        isTablet: type === 'tablet',\n        isDesktop: type === 'desktop'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3ZpZXdwb3J0LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVBOzs7Ozs7Q0FNQyxHQUNNLFNBQVNBLGdCQUFnQkMsS0FBYTtJQUMzQyxJQUFJQSxRQUFRLEtBQUssT0FBTztJQUN4QixJQUFJQSxRQUFRLE1BQU0sT0FBTztJQUN6QixPQUFPO0FBQ1Q7QUFFQTs7O0NBR0MsR0FDTSxTQUFTQztJQUNkLElBQUksSUFBNkIsRUFBRSxPQUFPO0lBQzFDLE9BQU9GLGdCQUFnQkcsT0FBT0MsVUFBVTtBQUMxQztBQUVPLFNBQVNDLFNBQVNKLEtBQWE7SUFDcEMsT0FBT0EsUUFBUTtBQUNqQjtBQUVPLFNBQVNLLFNBQVNMLEtBQWE7SUFDcEMsT0FBT0EsU0FBUyxPQUFPQSxRQUFRO0FBQ2pDO0FBRU8sU0FBU00sVUFBVU4sS0FBYTtJQUNyQyxPQUFPQSxTQUFTO0FBQ2xCO0FBRUE7OztDQUdDLEdBQ00sU0FBU087SUFDZCxNQUFNQyxPQUFPUDtJQUNiLE9BQU87UUFDTE87UUFDQUosVUFBVUksU0FBUztRQUNuQkgsVUFBVUcsU0FBUztRQUNuQkYsV0FBV0UsU0FBUztJQUN0QjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbG9tXFxEb2N1bWVudHNcXDAwMVdvcmtQcm9qZWN0XFxsZW5kYmxvY1xcdXNlclxcc3JjXFxsaWJcXHZpZXdwb3J0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB0eXBlIFZpZXdwb3J0VHlwZSA9ICdtb2JpbGUnIHwgJ3RhYmxldCcgfCAnZGVza3RvcCc7XHJcblxyXG4vKipcclxuICogRGV0ZXJtaW5lIHZpZXdwb3J0IHR5cGUgZnJvbSBhIGdpdmVuIHdpZHRoLlxyXG4gKiBCcmVha3BvaW50czpcclxuICogLSBtb2JpbGU6IDwgNzY4XHJcbiAqIC0gdGFibGV0OiA3NjggLSAxMDIzXHJcbiAqIC0gZGVza3RvcDog4omlIDEwMjRcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBnZXRWaWV3cG9ydFR5cGUod2lkdGg6IG51bWJlcik6IFZpZXdwb3J0VHlwZSB7XHJcbiAgaWYgKHdpZHRoIDwgNzY4KSByZXR1cm4gJ21vYmlsZSc7XHJcbiAgaWYgKHdpZHRoIDwgMTIwMCkgcmV0dXJuICd0YWJsZXQnO1xyXG4gIHJldHVybiAnZGVza3RvcCc7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBHZXQgdmlld3BvcnQgdHlwZSBmcm9tIGN1cnJlbnQgd2luZG93LmlubmVyV2lkdGguXHJcbiAqIFNhZmUgb24gc2VydmVyOiByZXR1cm5zICdkZXNrdG9wJyBhcyBhIHNlbnNpYmxlIGRlZmF1bHQuXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q3VycmVudFZpZXdwb3J0VHlwZSgpOiBWaWV3cG9ydFR5cGUge1xyXG4gIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuICdkZXNrdG9wJztcclxuICByZXR1cm4gZ2V0Vmlld3BvcnRUeXBlKHdpbmRvdy5pbm5lcldpZHRoKTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGlzTW9iaWxlKHdpZHRoOiBudW1iZXIpOiBib29sZWFuIHtcclxuICByZXR1cm4gd2lkdGggPCA3Njg7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBpc1RhYmxldCh3aWR0aDogbnVtYmVyKTogYm9vbGVhbiB7XHJcbiAgcmV0dXJuIHdpZHRoID49IDc2OCAmJiB3aWR0aCA8IDEyMDA7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBpc0Rlc2t0b3Aod2lkdGg6IG51bWJlcik6IGJvb2xlYW4ge1xyXG4gIHJldHVybiB3aWR0aCA+PSAxMjAwO1xyXG59XHJcblxyXG4vKipcclxuICogQ29udmVuaWVuY2UgZmxhZ3MgZnJvbSBjdXJyZW50IHdpbmRvdyB3aWR0aC5cclxuICogU2FmZSBvbiBzZXJ2ZXI6IGRlc2t0b3AgZGVmYXVsdHMuXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q3VycmVudFZpZXdwb3J0RmxhZ3MoKSB7XHJcbiAgY29uc3QgdHlwZSA9IGdldEN1cnJlbnRWaWV3cG9ydFR5cGUoKTtcclxuICByZXR1cm4ge1xyXG4gICAgdHlwZSxcclxuICAgIGlzTW9iaWxlOiB0eXBlID09PSAnbW9iaWxlJyxcclxuICAgIGlzVGFibGV0OiB0eXBlID09PSAndGFibGV0JyxcclxuICAgIGlzRGVza3RvcDogdHlwZSA9PT0gJ2Rlc2t0b3AnLFxyXG4gIH07XHJcbn0iXSwibmFtZXMiOlsiZ2V0Vmlld3BvcnRUeXBlIiwid2lkdGgiLCJnZXRDdXJyZW50Vmlld3BvcnRUeXBlIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImlzTW9iaWxlIiwiaXNUYWJsZXQiLCJpc0Rlc2t0b3AiLCJnZXRDdXJyZW50Vmlld3BvcnRGbGFncyIsInR5cGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/viewport.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/use-sync-external-store"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Faccount%2Fpage&page=%2Faccount%2Fpage&appPaths=%2Faccount%2Fpage&pagePath=private-next-app-dir%2Faccount%2Fpage.tsx&appDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Csolom%5CDocuments%5C001WorkProject%5Clendbloc%5Cuser&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();