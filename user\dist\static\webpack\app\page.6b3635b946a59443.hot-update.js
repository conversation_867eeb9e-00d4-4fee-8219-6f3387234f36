"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/LoanCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/LoanCard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoanCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,FileTerminal,FileType!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-type.js\");\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n/* harmony import */ var _utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/statusStyles */ \"(app-pages-browser)/./src/utils/statusStyles.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst getCryptoIcon = (crypto)=>{\n    switch(crypto){\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.BTC:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.ETH:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.USDT:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        default:\n            return _barrel_optimize_names_Bitcoin_FileTerminal_FileType_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n    }\n};\nconst getCryptoIconColor = (crypto)=>{\n    switch(crypto){\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.BTC:\n            return \"#F7931A\"; // Bitcoin orange\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.ETH:\n            return \"#627EEA\"; // Ethereum blue\n        case _types_enums__WEBPACK_IMPORTED_MODULE_2__.CryptoCurrency.USDT:\n            return \"#26A17B\"; // USDT green\n        default:\n            return \"#F7931A\";\n    }\n};\nfunction LoanCard(param) {\n    let { id, tokenTicker, loanAmount = 0, loanValueUSDT = 0, status, marginCallAmount = 0 } = param;\n    const statusVariant = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__.getStatusFromLoanHealth)(status);\n    const statusStyles = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_3__.getStatusStyles)(statusVariant);\n    const IconComponent = getCryptoIcon(tokenTicker);\n    const iconColor = getCryptoIconColor(tokenTicker);\n    // Add safety checks for numeric values\n    const safeLoanAmount = typeof loanAmount === 'number' && !isNaN(loanAmount) ? loanAmount : 0;\n    const safeLoanValueUSDT = typeof loanValueUSDT === 'number' && !isNaN(loanValueUSDT) ? loanValueUSDT : 0;\n    const safeMarginCallAmount = typeof marginCallAmount === 'number' && !isNaN(marginCallAmount) ? marginCallAmount : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0\",\n        style: {\n            backgroundColor: statusStyles.cardBG,\n            borderColor: statusStyles.cardBorderColour\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n            className: \"md:p-4 h-full flex flex-col justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center\",\n                                    style: {\n                                        backgroundColor: iconColor\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        size: 20,\n                                        color: \"white\",\n                                        className: \"md:w-6 md:h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm md:text-base font-semibold\",\n                                            children: [\n                                                safeLoanAmount,\n                                                \" \",\n                                                tokenTicker\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"$\",\n                                                safeLoanValueUSDT.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center rounded-full p-1 border\",\n                            style: {\n                                borderColor: statusStyles.statusBorderColour\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 md:w-3 md:h-3 rounded-full\",\n                                style: {\n                                    backgroundColor: statusStyles.statusColour\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 md:mt-4 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-muted-foreground capitalize\",\n                            children: \"Margin call\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs md:text-sm font-semibold tracking-tight\",\n                            children: [\n                                \"$\",\n                                safeMarginCallAmount.toLocaleString()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\dashboard\\\\LoanCard.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = LoanCard;\nvar _c;\n$RefreshReg$(_c, \"LoanCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/LoanCard.tsx\n"));

/***/ })

});