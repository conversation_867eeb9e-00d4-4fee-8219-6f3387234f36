// Status styles helper
// Provides three status variants and helpers to select styles.

export type StatusVariant = "good" | "warning" | "danger";

export interface StatusStyle {
  // color used for inline status text/icon
  statusColour: string;
  // border color used for small status badges
  statusBorderColour: string;
  // card background color
  cardBG: string;
  // card border color
  cardBorderColour: string;
}

/**
 * Hex color based style map for three status variants.
 * Use these values directly as `style` properties in components.
 */
export const STATUS_STYLES: Record<StatusVariant, StatusStyle> = {
  good: {
    statusColour: "#19D539",
    statusBorderColour: "#FFFFFF",
    cardBG: "#FFFFFF",
    cardBorderColour: "#FFFFFF",
  },
  warning: {
    statusColour: "#F6A70A",
    statusBorderColour: "#F6A70A",
    cardBG: "#FFF7E6",
    cardBorderColour: "#F6A70A",
  },
  danger: {
    statusColour: "#FF5353",
    statusBorderColour: "#FF5353",
    cardBG: "#FFF2F2",
    cardBorderColour: "#FF5353",
  },
};

/**
 * Return the style object for a given variant. Defaults to `warning` if unknown.
 */
export function getStatusStyles(variant?: StatusVariant): StatusStyle {
  if (!variant || !(variant in STATUS_STYLES)) return STATUS_STYLES.warning;
  return STATUS_STYLES[variant];
}

/**
 * Simple heuristic to map a numeric score (0-100) to a status variant.
 * - >= 80 => good
 * - >= 50 => warning
 * - < 50 => danger
 * Useful when backend returns a score/health metric and you want a visual status.
 */
export function getStatusFromScore(score: number | null | undefined): StatusVariant {
  if (typeof score !== "number" || Number.isNaN(score)) return "warning";
  if (score >= 80) return "good";
  if (score >= 50) return "warning";
  return "danger";
}

/**
 * Map LoanHealth enum to StatusVariant
 */
export function getStatusFromLoanHealth(health: string): StatusVariant {
  switch (health.toLowerCase()) {
    case 'green':
      return 'good';
    case 'yellow':
      return 'warning';
    case 'red':
      return 'danger';
    default:
      return 'warning';
  }
}

/**
 * Convenience: return both variant and styles from a score
 */
export function statusFromScoreWithStyles(score?: number | null) {
  const variant = getStatusFromScore(score ?? null);
  return { variant, styles: getStatusStyles(variant) };
}