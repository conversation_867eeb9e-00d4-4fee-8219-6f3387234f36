import { CryptoCurrency, LoanHealth } from '../types/enums';

export interface LoanData {
  id: string;
  tokenTicker: CryptoCurrency;
  loanAmount: number;
  loanValueUSDT: number;
  status: LoanHealth;
  marginCallAmount: number;
}

export interface InterestDataPoint {
  month: string;
  interest: number;
}

// Mock data for the interest chart
export const mockInterestData: InterestDataPoint[] = [
  { month: 'Jan', interest: 520 },
  { month: 'Feb', interest: 480 },
  { month: 'Mar', interest: 590 },
  { month: 'Apr', interest: 510 },
  { month: 'May', interest: 620 },
  { month: 'Jun', interest: 580 },
  { month: 'Jul', interest: 650 },
  { month: 'Aug', interest: 720 }
];

// Mock data for loan cards
export const mockLoanData: LoanData[] = [
  {
    id: '1',
    tokenTicker: CryptoCurrency.BTC,
    loanAmount: 0.0025,
    loanValueUSDT: 250.00,
    status: LoanHealth.GREEN,
    marginCallAmount: 90568.21,
  },
  {
    id: '2',
    tokenTicker: CryptoCurrency.ETH,
    loanAmount: 0.0090,
    loanValueUSDT: 20.00,
    status: LoanHealth.YELLOW,
    marginCallAmount: 1800.36,
  },
  {
    id: '3',
    tokenTicker: CryptoCurrency.BTC,
    loanAmount: 0.0025,
    loanValueUSDT: 250.00,
    status: LoanHealth.RED,
    marginCallAmount: 90568.21,
  },
  {
    id: '4',
    tokenTicker: CryptoCurrency.USDT,
    loanAmount: 1000,
    loanValueUSDT: 1000.00,
    status: LoanHealth.GREEN,
    marginCallAmount: 950.00,
  },
];

// Data passed as props to the root component
export const mockRootProps = {
  interestData: mockInterestData,
  loanData: mockLoanData,
  loans: [
    {
      id: '1',
      collateralAmount: 0.0025,
      collateralCurrency: CryptoCurrency.BTC as const,
      collateralValueUSD: 250,
      currentRate: 97568.21,
      marginCall: 90568.21,
      ratePair: `${CryptoCurrency.BTC}/${CryptoCurrency.USDT}` as const,
      health: LoanHealth.GREEN as const,
      isHighlighted: true
    },
    {
      id: '2',
      collateralAmount: 0.0090,
      collateralCurrency: CryptoCurrency.ETH as const,
      collateralValueUSD: 20,
      currentRate: 2477.21,
      marginCall: 1800.36,
      ratePair: `${CryptoCurrency.ETH}/${CryptoCurrency.USDT}` as const,
      health: LoanHealth.GREEN as const,
      isHighlighted: false
    },
    {
      id: '3',
      collateralAmount: 0.0025,
      collateralCurrency: CryptoCurrency.BTC as const,
      collateralValueUSD: 250,
      currentRate: 97568.21,
      marginCall: 90568.21,
      ratePair: `${CryptoCurrency.BTC}/${CryptoCurrency.USDT}` as const,
      health: LoanHealth.GREEN as const,
      isHighlighted: false
    },
    {
      id: '4',
      collateralAmount: 0.0025,
      collateralCurrency: CryptoCurrency.BTC as const,
      collateralValueUSD: 250,
      currentRate: 97568.21,
      marginCall: 90568.21,
      ratePair: `${CryptoCurrency.BTC}/${CryptoCurrency.USDT}` as const,
      health: LoanHealth.GREEN as const,
      isHighlighted: false
    },
    {
      id: '5',
      collateralAmount: 0.0090,
      collateralCurrency: CryptoCurrency.ETH as const,
      collateralValueUSD: 20,
      currentRate: 2477.21,
      marginCall: 1800.36,
      ratePair: `${CryptoCurrency.ETH}/${CryptoCurrency.USDT}` as const,
      health: LoanHealth.GREEN as const,
      isHighlighted: false
    }
  ],
  portfolioInsights: {
    totalValue: 2154.00,
    interestEarnedData: [
      { month: 'Jan', value: 520 },
      { month: 'Feb', value: 480 },
      { month: 'Mar', value: 590 },
      { month: 'Apr', value: 510 },
      { month: 'May', value: 620 },
      { month: 'Jun', value: 580 },
      { month: 'Jul', value: 650 },
      { month: 'Aug', value: 720 }
    ],
    holdings: [
      {
        currency: CryptoCurrency.BTC as const,
        name: 'Bitcoin',
        value: 18365.12,
        change: 2.45,
        isPositive: true
      },
      {
        currency: CryptoCurrency.ETH as const,
        name: 'Ethereum',
        value: 13365.12,
        change: 1.85,
        isPositive: true
      },
      {
        currency: CryptoCurrency.USDT as const,
        name: 'Tether',
        value: 203.00,
        change: -0.12,
        isPositive: false
      }
    ]
  }
};