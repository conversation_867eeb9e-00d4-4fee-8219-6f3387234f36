"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/FirstLandingDash */ \"(app-pages-browser)/./src/components/dashboard/FirstLandingDash.tsx\");\n/* harmony import */ var _components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/TopTrendsTable */ \"(app-pages-browser)/./src/components/dashboard/TopTrendsTable.tsx\");\n/* harmony import */ var _components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/dashboard/NewListedCoins */ \"(app-pages-browser)/./src/components/dashboard/NewListedCoins.tsx\");\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/dashboard/MainDash */ \"(app-pages-browser)/./src/components/dashboard/MainDash.tsx\");\n/* harmony import */ var _components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/AccountTabContent */ \"(app-pages-browser)/./src/components/dashboard/AccountTabContent.tsx\");\n/* harmony import */ var _components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/TransactionsTabContent */ \"(app-pages-browser)/./src/components/dashboard/TransactionsTabContent.tsx\");\n/* harmony import */ var _lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/dashboard-data */ \"(app-pages-browser)/./src/lib/dashboard-data.ts\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardHomePage() {\n    _s();\n    const { isMobile } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType)();\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showMainDash, setShowMainDash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceToggle, setForceToggle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch dashboard data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardHomePage.useEffect\": ()=>{\n            const loadDashboardData = {\n                \"DashboardHomePage.useEffect.loadDashboardData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const data = await (0,_lib_dashboard_data__WEBPACK_IMPORTED_MODULE_13__.fetchDashboardData)();\n                        setDashboardData(data);\n                        setShowMainDash(data.hasUserData);\n                    } catch (error) {\n                        console.error('Failed to load dashboard data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"DashboardHomePage.useEffect.loadDashboardData\"];\n            loadDashboardData();\n        }\n    }[\"DashboardHomePage.useEffect\"], []);\n    // Handle toggle between dashboards\n    const handleToggleDashboard = ()=>{\n        setForceToggle(!forceToggle);\n        setShowMainDash(!showMainDash);\n    };\n    var _dashboardData_hasUserData;\n    // Determine which dashboard to show\n    const shouldShowMainDash = forceToggle ? showMainDash : (_dashboardData_hasUserData = dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.hasUserData) !== null && _dashboardData_hasUserData !== void 0 ? _dashboardData_hasUserData : false;\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (!dashboardData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600\",\n                    children: \"Failed to load dashboard data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                    htmlFor: \"dashboard-toggle\",\n                                    className: \"text-sm font-medium\",\n                                    children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    id: \"dashboard-toggle\",\n                                    checked: shouldShowMainDash,\n                                    onCheckedChange: handleToggleDashboard\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0\",\n                        children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                            loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            ltvChartData: dashboardData.ltvChartData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 mt-4 min-h-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"updates\",\n                                className: \"h-full flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        className: \"grid w-full grid-cols-3 mb-4 bg-gray-100 p-1 rounded-full h-12 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"updates\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Updates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"account\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"transactions\",\n                                                className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                                children: \"Transactions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-h-0 overflow-y-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"updates\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            trends: dashboardData.topTrendsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            coins: dashboardData.newCoinsData\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"account\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                                value: \"transactions\",\n                                                className: \"space-y-4 px-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this);\n    }\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full grid grid-cols-1 gap-4 lg:grid-cols-[6fr_4fr] 2xl:grid-cols-[7fr_3fr]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0 mb-4 p-3 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"dashboard-toggle-desktop\",\n                                        className: \"text-sm font-medium\",\n                                        children: shouldShowMainDash ? \"Main Dashboard\" : \"First Landing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                        id: \"dashboard-toggle-desktop\",\n                                        checked: shouldShowMainDash,\n                                        onCheckedChange: handleToggleDashboard\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0\",\n                            children: shouldShowMainDash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_MainDash__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                interestData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockInterestData,\n                                loanData: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_14__.mockLoanData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FirstLandingDash__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ltvChartData: dashboardData.ltvChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"h-full w-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            defaultValue: \"updates\",\n                            className: \"h-full flex flex-col bg-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid w-full grid-cols-3 mb-4 p-1 bg-transparent rounded-full h-12 flex-shrink-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"updates\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Updates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"account\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"transactions\",\n                                            className: \"data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full\",\n                                            children: \"Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-h-full min-w-full overflow-y-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"updates\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TopTrendsTable__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        trends: dashboardData.topTrendsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_NewListedCoins__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        coins: dashboardData.newCoinsData\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"account\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AccountTabContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: \"transactions\",\n                                            className: \"space-y-4 px-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TransactionsTabContent__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardHomePage, \"JIBGPP0NwMrIF0bqKkCUv4RQCLM=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_9__.useViewportType\n    ];\n});\n_c = DashboardHomePage;\nvar _c;\n$RefreshReg$(_c, \"DashboardHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});